#!/usr/bin/env python3
"""
Enhanced memory management for ab initio predictions to prevent OOM errors.
"""

import os
import sys
import time
from typing import List, Dict, <PERSON><PERSON>

def enhanced_abinitio_wrapper(contig, params, logger, monitor_memory=False, memory_limit_gb=None):
    """
    Enhanced ab initio wrapper with pre-launch memory checking.
    
    This version checks available memory before launching each tool to prevent OOM errors.
    """
    
    # Get contig info
    contig_name = os.path.basename(contig)
    memory_stats = {}
    
    # Import memory functions if monitoring is enabled
    if monitor_memory:
        try:
            from funannotate2.memory import (
                get_contig_length, 
                predict_memory_usage, 
                get_system_memory_info
            )
            
            contig_length = get_contig_length(contig)
            
            # Log contig processing message
            log_message = f"Processing contig {contig_name} (length: {contig_length:,} bp)"
            try:
                if hasattr(logger, "info"):
                    logger.info(log_message)
                elif callable(logger):
                    logger(log_message + "\n")
                else:
                    print(log_message)
            except Exception:
                print(log_message)
                
        except ImportError:
            logger.warning("Memory monitoring requested but memory module not available")
            monitor_memory = False
    
    # Create log function wrapper
    def log_func(message):
        try:
            if hasattr(logger, "debug"):
                logger.debug(message)
            elif callable(logger):
                logger(message + "\n")
            else:
                print(message)
        except Exception:
            print(message)
    
    # Enhanced memory checking function
    def check_memory_before_launch(tool_name, contig_length, memory_limit_gb=None):
        """Check if there's enough memory available before launching a tool."""
        if not monitor_memory:
            return True  # Skip check if monitoring disabled
        
        try:
            # Get memory prediction
            prediction = predict_memory_usage(tool_name, contig_length)
            predicted_mb = prediction['predicted_peak_with_margin_mb']
            
            # Get current system memory
            memory_info = get_system_memory_info()
            if "error" in memory_info:
                logger.warning(f"Could not get system memory info: {memory_info['error']}")
                return True  # Proceed if we can't check
            
            available_mb = memory_info['available_gb'] * 1024
            
            # Check against memory limit if specified
            if memory_limit_gb:
                limit_mb = memory_limit_gb * 1024
                if predicted_mb > limit_mb:
                    logger.error(
                        f"MEMORY LIMIT EXCEEDED: {tool_name} predicted to use {predicted_mb:.1f} MB "
                        f"but limit is {limit_mb:.1f} MB for contig {contig_name}"
                    )
                    return False
            
            # Check against available system memory (with 20% buffer)
            usable_mb = available_mb * 0.8
            if predicted_mb > usable_mb:
                logger.error(
                    f"INSUFFICIENT MEMORY: {tool_name} predicted to use {predicted_mb:.1f} MB "
                    f"but only {usable_mb:.1f} MB available for contig {contig_name}"
                )
                return False
            
            # Log prediction
            log_message = f"{tool_name} memory prediction for {contig_name}: {predicted_mb:.1f} MB"
            try:
                if hasattr(logger, "info"):
                    logger.info(log_message)
                elif callable(logger):
                    logger(log_message + "\n")
                else:
                    print(log_message)
            except Exception:
                print(log_message)
            
            return True
            
        except Exception as e:
            logger.warning(f"Error checking memory for {tool_name}: {e}")
            return True  # Proceed if check fails
    
    # Import ab initio functions
    from funannotate2.abinitio import run_snap, run_glimmerhmm, run_augustus, run_genemark
    
    # Run ab initio predictions with memory checking
    tools_run = []
    tools_skipped = []
    
    if "snap" in params["abinitio"]:
        if check_memory_before_launch("snap", contig_length if monitor_memory else 1000000, memory_limit_gb):
            run_snap(
                contig,
                params["abinitio"]["snap"]["location"],
                os.path.join(os.path.dirname(contig), f"{contig_name}.snap.gff3"),
                log=log_func,
            )
            tools_run.append("snap")
        else:
            tools_skipped.append("snap")

    if "glimmerhmm" in params["abinitio"]:
        if check_memory_before_launch("glimmerhmm", contig_length if monitor_memory else 1000000, memory_limit_gb):
            run_glimmerhmm(
                contig,
                params["abinitio"]["glimmerhmm"]["location"],
                os.path.join(os.path.dirname(contig), f"{contig_name}.glimmerhmm.gff3"),
                log=log_func,
            )
            tools_run.append("glimmerhmm")
        else:
            tools_skipped.append("glimmerhmm")

    if "genemark" in params["abinitio"]:
        if check_memory_before_launch("genemark", contig_length if monitor_memory else 1000000, memory_limit_gb):
            run_genemark(
                contig,
                params["abinitio"]["genemark"]["location"],
                os.path.join(os.path.dirname(contig), f"{contig_name}.genemark.gff3"),
                log=log_func,
            )
            tools_run.append("genemark")
        else:
            tools_skipped.append("genemark")

    if "augustus" in params["abinitio"]:
        hints = False
        hintsfile = os.path.join(
            os.path.dirname(contig),
            f"{contig_name}.hintsfile",
        )
        if os.path.isfile(hintsfile):
            hints = hintsfile

        if check_memory_before_launch("augustus", contig_length if monitor_memory else 1000000, memory_limit_gb):
            run_augustus(
                contig,
                params["abinitio"]["augustus"]["species"],
                os.path.join(os.path.dirname(contig), f"{contig_name}.augustus.gff3"),
                log=log_func,
                hints=hints,
                config_path=params["abinitio"]["augustus"]["location"],
            )
            tools_run.append("augustus")
        else:
            tools_skipped.append("augustus")
    
    # Log results
    if tools_skipped:
        logger.warning(
            f"Skipped tools due to memory constraints for {contig_name}: {', '.join(tools_skipped)}"
        )
    
    if tools_run:
        logger.info(f"Successfully ran tools for {contig_name}: {', '.join(tools_run)}")
    
    return memory_stats if monitor_memory else None


def enhanced_memory_aware_job_scheduling(abinit_cmds, cpus, memory_limit_gb=None, logger=None):
    """
    Enhanced job scheduling that considers memory constraints.
    
    Instead of launching all jobs at once, this schedules jobs based on memory availability.
    """
    
    if not memory_limit_gb:
        # If no memory limit specified, use original behavior
        from funannotate2.utilities import runProcessJob
        return runProcessJob(enhanced_abinitio_wrapper, abinit_cmds, cpus=cpus)
    
    try:
        from funannotate2.memory import get_system_memory_info, predict_memory_usage, get_contig_length
    except ImportError:
        logger.warning("Memory module not available, using original job scheduling")
        from funannotate2.utilities import runProcessJob
        return runProcessJob(enhanced_abinitio_wrapper, abinit_cmds, cpus=cpus)
    
    # Get system memory info
    memory_info = get_system_memory_info()
    if "error" in memory_info:
        logger.warning(f"Could not get memory info: {memory_info['error']}, using original scheduling")
        from funannotate2.utilities import runProcessJob
        return runProcessJob(enhanced_abinitio_wrapper, abinit_cmds, cpus=cpus)
    
    available_mb = memory_info['available_gb'] * 1024
    usable_mb = min(available_mb * 0.8, memory_limit_gb * 1024)  # 80% of available or limit
    
    logger.info(f"Memory-aware scheduling: {usable_mb:.1f} MB available for ab initio predictions")
    
    # Estimate memory requirements for each job
    job_estimates = []
    for cmd in abinit_cmds:
        contig, params, _, monitor_memory = cmd
        
        try:
            contig_length = get_contig_length(contig)
            
            # Estimate memory for all tools that will run on this contig
            max_memory = 0
            for tool in params["abinitio"].keys():
                prediction = predict_memory_usage(tool, contig_length)
                tool_memory = prediction['predicted_peak_with_margin_mb']
                max_memory = max(max_memory, tool_memory)
            
            job_estimates.append((cmd, max_memory, contig_length))
            
        except Exception as e:
            logger.warning(f"Could not estimate memory for {contig}: {e}")
            job_estimates.append((cmd, 500, 1000000))  # Default estimate
    
    # Sort jobs by memory requirement (largest first)
    job_estimates.sort(key=lambda x: x[1], reverse=True)
    
    # Process jobs in batches that fit in memory
    results = []
    processed = 0
    
    while processed < len(job_estimates):
        # Find jobs that can fit in current memory budget
        current_batch = []
        current_memory = 0
        
        for i in range(processed, len(job_estimates)):
            cmd, estimated_memory, contig_length = job_estimates[i]
            
            if current_memory + estimated_memory <= usable_mb and len(current_batch) < cpus:
                current_batch.append(cmd)
                current_memory += estimated_memory
                processed += 1
            else:
                break
        
        if not current_batch:
            # If we can't fit even one job, process it anyway with warning
            cmd, estimated_memory, contig_length = job_estimates[processed]
            logger.warning(
                f"Job requires {estimated_memory:.1f} MB but only {usable_mb:.1f} MB available. "
                f"Processing anyway but OOM risk is high."
            )
            current_batch = [cmd]
            processed += 1
        
        # Process current batch
        logger.info(
            f"Processing batch of {len(current_batch)} jobs "
            f"(estimated memory: {current_memory:.1f} MB)"
        )
        
        from funannotate2.utilities import runProcessJob
        batch_results = runProcessJob(enhanced_abinitio_wrapper, current_batch, cpus=len(current_batch))
        results.extend(batch_results)
    
    return results


if __name__ == "__main__":
    print("Enhanced memory management functions for funannotate2")
    print("This module provides:")
    print("1. Pre-launch memory checking to prevent OOM errors")
    print("2. Memory-aware job scheduling")
    print("3. Enhanced logging of memory constraints")
