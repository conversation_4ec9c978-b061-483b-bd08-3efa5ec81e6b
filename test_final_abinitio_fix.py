#!/usr/bin/env python3
"""
Test script to verify the final ab initio logger fix.
"""

import os
import sys
import tempfile
import logging

# Add funannotate2 to path for testing
sys.path.insert(0, '/Users/<USER>/software/funannotate2')

def create_test_contig(length_bp, filename):
    """Create a test FASTA file with specified length."""
    with open(filename, 'w') as f:
        f.write(">test_contig\n")
        sequence = "ATCG" * (length_bp // 4)
        for i in range(0, len(sequence), 80):
            f.write(sequence[i:i+80] + "\n")
    return filename

def test_final_abinitio_fix():
    """Test that all ab initio logger issues are fixed."""
    
    try:
        from funannotate2.predict import abinitio_wrapper
        from funannotate2.utilities import runProcessJob
        from funannotate2.log import startLogging
        
        print("Testing final ab initio logger fix...")
        
        # Create logger exactly like funannotate2 does
        with tempfile.TemporaryDirectory() as tmpdir:
            log_file = os.path.join(tmpdir, "test.log")
            logger = startLogging(logfile=log_file)
            
            # Create test parameters
            params = {
                "abinitio": {
                    # Empty - no tools will actually run, just testing the logger compatibility
                }
            }
            
            # Create test contigs
            contigs = []
            for i in range(3):  # Test with multiple contigs
                contig_file = os.path.join(tmpdir, f"test_contig_{i}.fasta")
                create_test_contig(5000, contig_file)
                contigs.append(contig_file)
            
            # Test 1: Direct calls
            print("Test 1: Direct calls with logger object")
            for i, contig in enumerate(contigs):
                try:
                    result = abinitio_wrapper(contig, params, logger, monitor_memory=True)
                    print(f"✓ Direct call {i+1} with logger object works")
                except Exception as e:
                    print(f"✗ Error with direct call {i+1}: {e}")
                    return False
            
            # Test 2: Through runProcessJob (the real scenario)
            print("Test 2: Through runProcessJob with memory monitoring")
            try:
                abinit_cmds = [(c, params, logger) for c in contigs]
                results = runProcessJob(abinitio_wrapper, abinit_cmds, cpus=2, monitor_memory=True)
                print("✓ runProcessJob with memory monitoring works")
                print(f"✓ Processed {len(results)} contigs successfully")
            except Exception as e:
                print(f"✗ Error with runProcessJob: {e}")
                return False
            
            # Test 3: Test with function logger (the problematic case)
            print("Test 3: With function logger")
            def func_logger(message):
                print(f"FUNC_LOG: {message}")
            
            try:
                abinit_cmds = [(c, params, func_logger) for c in contigs]
                results = runProcessJob(abinitio_wrapper, abinit_cmds, cpus=2, monitor_memory=True)
                print("✓ Function logger with multiprocessing works")
            except Exception as e:
                print(f"✗ Error with function logger: {e}")
                return False
            
            # Test 4: Test with sys.stderr.write (original expected type)
            print("Test 4: With sys.stderr.write")
            try:
                abinit_cmds = [(c, params, sys.stderr.write) for c in contigs]
                results = runProcessJob(abinitio_wrapper, abinit_cmds, cpus=2, monitor_memory=True)
                print("✓ sys.stderr.write with multiprocessing works")
            except Exception as e:
                print(f"✗ Error with sys.stderr.write: {e}")
                return False
                
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    
    return True

def main():
    """Run the test."""
    print("Testing final ab initio logger fix")
    print("=" * 40)
    
    success = test_final_abinitio_fix()
    
    if success:
        print("\n✓ All tests passed!")
        print("The final ab initio logger fix should resolve all remaining errors.")
    else:
        print("\n✗ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
