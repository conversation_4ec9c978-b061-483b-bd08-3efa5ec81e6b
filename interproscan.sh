#!/usr/bin/env bash

# this is a hacky way to run dockerized linux application locally

realpath() {
  OURPWD=$PWD
  cd "$(dirname "$1")"
  LINK=$(readlink "$(basename "$1")")
  while [ "$LINK" ]; do
    cd "$(dirname "$LINK")"
    LINK=$(readlink "$(basename "$1")")
  done
  REALPATH="$PWD/$(basename "$1")"
  cd "$OURPWD"
  echo "$REALPATH"
}

timezone() {
    if [ "$(uname)" == "Darwin" ]; then
        TZ=$(readlink /etc/localtime | sed 's#/var/db/timezone/zoneinfo/##')
    else
        TZ=$(readlink /etc/timezone)
    fi
    echo $TZ
}


# Only allocate tty if one is detected. See - https://stackoverflow.com/questions/911168
if [[ -t 0 ]]; then IT+=(-i); fi
if [[ -t 1 ]]; then IT+=(-t); fi

WORKDIR="$(realpath .)"
MOUNT="type=bind,source=${WORKDIR},target=${WORKDIR}"
TZ="$(timezone)"

args=()
while [[ $# -gt 0 ]]; do
    case "$1" in
        --input|-i)
            # Convert absolute paths to be relative to /work
            if [[ "$2" == /* ]]; then
                # For absolute paths, make them relative to WORKDIR
                REL_PATH="${2#$WORKDIR/}"
                args+=("$1" "/work/$REL_PATH")
            else
                # For relative paths, just prepend /work/
                args+=("$1" "/work/$2")
            fi
            shift 2
            ;;
        --outfile|-o)
            # Convert absolute paths to be relative to /work
            if [[ "$2" == /* ]]; then
                # For absolute paths, make them relative to WORKDIR
                REL_PATH="${2#$WORKDIR/}"
                args+=("$1" "/work/$REL_PATH")
            else
                # For relative paths, just prepend /work/
                args+=("$1" "/work/$2")
            fi
            shift 2
            ;;
        *)
            args+=("$1")
            shift
            ;;
    esac
done

# check all arguments for paths to use as mount points
SCRIPT=$(realpath "$0")
SCRIPTPATH=$(dirname "$SCRIPT")

all_mounts=$($SCRIPTPATH/dockerized-parser-interpro.py "$WORKDIR" "$@")

cmd="docker run --rm "${IT[@]}" -e TZ="${TZ}" $all_mounts -v /Users/<USER>/software/bin/interproscan-5.74-105.0/data:/opt/interproscan/data interpro/interproscan:5.74-105.0 ${args[@]}"

exec $cmd
