#!/bin/bash
# Simple GeneMark Training Test Script
# Usage: ./simple_genemark_test.sh <genome.fa> <species_name> [cpus]

GENOME="$1"
SPECIES="$2"
CPUS="${3:-8}"

if [ $# -lt 2 ]; then
    echo "Usage: $0 <genome.fa> <species_name> [cpus]"
    echo "Example: $0 FGSCA4.fna 'Aspergillus nidulans' 8"
    exit 1
fi

if [ ! -f "$GENOME" ]; then
    echo "Error: Genome file not found: $GENOME"
    exit 1
fi

echo "=== GeneMark Training Mode Test ==="
echo "Genome: $GENOME"
echo "Species: $SPECIES"
echo "CPUs: $CPUS"
echo

# Test 1: Hybrid mode (default)
echo "=== Testing Hybrid Mode ==="
echo "Command: funannotate2 train -f $GENOME -s '$SPECIES' -o hybrid_test --cpus $CPUS --genemark-mode hybrid"
echo

start_time=$(date +%s)
funannotate2 train -f "$GENOME" -s "$SPECIES" -o hybrid_test --cpus "$CPUS" --genemark-mode hybrid
hybrid_exit_code=$?
end_time=$(date +%s)
hybrid_duration=$((end_time - start_time))

echo
if [ $hybrid_exit_code -eq 0 ]; then
    echo "✅ Hybrid training completed successfully in ${hybrid_duration}s ($((hybrid_duration/60))m $((hybrid_duration%60))s)"
    
    # Check if GeneMark model was created
    if [ -f "hybrid_test/train_misc/genemark/gmhmm.mod" ]; then
        model_size=$(wc -l < "hybrid_test/train_misc/genemark/gmhmm.mod")
        echo "✅ GeneMark model created: ${model_size} lines"
    else
        echo "⚠️  GeneMark model file not found"
    fi
    
    # Check training parameters
    if [ -f "hybrid_test/train_misc/training-params.json" ]; then
        echo "✅ Training parameters file created"
    else
        echo "⚠️  Training parameters file not found"
    fi
else
    echo "❌ Hybrid training failed with exit code: $hybrid_exit_code"
    echo "Check logs in: hybrid_test/logs/"
fi

echo
echo "=== Testing Self Mode ==="
echo "Command: funannotate2 train -f $GENOME -s '$SPECIES' -o self_test --cpus $CPUS --genemark-mode self"
echo "⚠️  This may take significantly longer..."
echo

start_time=$(date +%s)
funannotate2 train -f "$GENOME" -s "$SPECIES" -o self_test --cpus "$CPUS" --genemark-mode self
self_exit_code=$?
end_time=$(date +%s)
self_duration=$((end_time - start_time))

echo
if [ $self_exit_code -eq 0 ]; then
    echo "✅ Self training completed successfully in ${self_duration}s ($((self_duration/60))m $((self_duration%60))s)"
    
    # Check if GeneMark model was created
    if [ -f "self_test/train_misc/genemark/gmhmm.mod" ]; then
        model_size=$(wc -l < "self_test/train_misc/genemark/gmhmm.mod")
        echo "✅ GeneMark model created: ${model_size} lines"
    else
        echo "⚠️  GeneMark model file not found"
    fi
    
    # Check training parameters
    if [ -f "self_test/train_misc/training-params.json" ]; then
        echo "✅ Training parameters file created"
    else
        echo "⚠️  Training parameters file not found"
    fi
else
    echo "❌ Self training failed with exit code: $self_exit_code"
    echo "Check logs in: self_test/logs/"
fi

echo
echo "=== Comparison Summary ==="

if [ $hybrid_exit_code -eq 0 ] && [ $self_exit_code -eq 0 ]; then
    echo "Both training modes completed successfully!"
    echo "Hybrid time: ${hybrid_duration}s ($((hybrid_duration/60))m $((hybrid_duration%60))s)"
    echo "Self time: ${self_duration}s ($((self_duration/60))m $((self_duration%60))s)"
    
    if [ $hybrid_duration -gt 0 ] && [ $self_duration -gt 0 ]; then
        speedup=$((self_duration / hybrid_duration))
        echo "Speedup: ${speedup}x faster with hybrid mode"
    fi
    
    # Compare model files
    if [ -f "hybrid_test/train_misc/genemark/gmhmm.mod" ] && [ -f "self_test/train_misc/genemark/gmhmm.mod" ]; then
        hybrid_model_size=$(wc -l < "hybrid_test/train_misc/genemark/gmhmm.mod")
        self_model_size=$(wc -l < "self_test/train_misc/genemark/gmhmm.mod")
        echo "Hybrid model: ${hybrid_model_size} lines"
        echo "Self model: ${self_model_size} lines"
    fi
    
    echo
    echo "Next steps to test prediction quality:"
    echo "funannotate2 predict -f $GENOME -p hybrid_test/train_misc/training-params.json -o hybrid_predict"
    echo "funannotate2 predict -f $GENOME -p self_test/train_misc/training-params.json -o self_predict"
    echo "wc -l */predict_results/*.gff3"
    
elif [ $hybrid_exit_code -eq 0 ]; then
    echo "✅ Hybrid training succeeded"
    echo "❌ Self training failed"
    echo "Hybrid appears to be more robust!"
    
elif [ $self_exit_code -eq 0 ]; then
    echo "❌ Hybrid training failed"
    echo "✅ Self training succeeded"
    echo "Need to investigate hybrid training issues"
    
else
    echo "❌ Both training modes failed"
    echo "Check the logs for errors:"
    echo "  Hybrid logs: hybrid_test/logs/"
    echo "  Self logs: self_test/logs/"
fi

echo
echo "=== Log File Locations ==="
echo "Hybrid training logs: hybrid_test/logs/funannotate2-train.log"
echo "Self training logs: self_test/logs/funannotate2-train.log"
echo
echo "To check for specific GeneMark messages:"
echo "grep -i genemark */logs/funannotate2-train.log"
echo "grep -i 'training mode' */logs/funannotate2-train.log"
