{% set name = "mappy" %}
{% set version = "2.30" %}

package:
  name: {{ name }}
  version: {{ version }}

source:
  url: https://pypi.io/packages/source/{{ name[0] }}/{{ name }}/mappy-{{ version }}.tar.gz
  sha256: a25448004558a28cb0d74fb1e55b6ffe9a78aa15dd6b2763630fbbabbaa97a27

build:
  number: 0
  run_exports:
    - {{ pin_subpackage('mappy', max_pin="x") }}

requirements:
  build:
    - {{ compiler('c') }}
  host:
    - python
    - cython
    - pip
    - zlib
  run:
    - python

test:
  imports:
    - mappy

about:
  home: https://github.com/lh3/minimap2
  license: MIT
  license_family: MIT
  license_file: LICENSE.txt
  summary: 'Minimap2 Python binding'

extra:
  additional-platforms:
    - linux-aarch64
    - osx-arm64
