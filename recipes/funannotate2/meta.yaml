{% set name = "funannotate2" %}
{% set version = "25.7.1" %}

package:
  name: {{ name|lower }}
  version: {{ version }}

source:
  url: https://pypi.org/packages/source/{{ name[0] }}/{{ name }}/funannotate2-{{ version }}.tar.gz
  sha256: 34bcc8ac619886e905ddfd9a0d35e5a33e42a277d27dc761149034708d60d576

build:
  number: 0
  noarch: python
  entry_points:
    - funannotate2 = funannotate2.__main__:main
  script: {{ PYTHON }} -m pip install . --no-deps --no-build-isolation --no-cache-dir -vvv
  run_exports:
    - {{ pin_subpackage('funannotate2', max_pin=None) }}

requirements:
  host:
    - python >=3.7
    - pip
    - hatchling
  run:
    - python >=3.7
    - natsort
    - numpy
    - mappy
    - gfftk >=25.6.10
    - buscolite >=25.4.24
    - gapmm2 >=25.4.13
    - psutil
    - pyhmmer >=0.10.15
    - pyfastx >=2.0.0
    - requests
    - gb-io >=0.3.2
    - json-repair
    - pytantan
    - augustus >=3.5.0
    - snap
    - glimmerhmm
    - diamond
    - trnascan-se
    - table2asn
    
test:
  imports:
    - funannotate2
  commands:
    - funannotate2 --help

about:
  home: "https://github.com/nextgenusfs/funannotate2"
  summary: "Funannotate2: eukarytoic genome annotation pipeline."
  license: "BSD-2-Clause"
  license_family: BSD
  license_file: LICENSE
  dev_url: "https://github.com/nextgenusfs/funannotate2"
  doc_url: "https://github.com/nextgenusfs/funannotate2/blob/{{ version }}/README.md"

extra:
  recipe-maintainers:
    - nextgenusfs
