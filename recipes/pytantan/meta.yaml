{% set name = "pytantan" %}
{% set version = "0.1.3" %}

package:
  name: {{ name }}
  version: {{ version }}

source:
  url: https://pypi.org/packages/source/{{ name[0] }}/{{ name }}/pytantan-{{ version }}.tar.gz
  sha256: b1f8addeeb7955a1a7ca57f08415bcc7a36f95f30d23d477f6075d2cce025c4a
  patches:
    - 0001-main.patch

build:
  number: 1
  skip: True  # [py < 37]
  script:
    - export LDFLAGS="${LDFLAGS} -L${PREFIX}/lib"
    - export CPPFLAGS="${CPPFLAGS} -I${PREFIX}/include"
    - export CFLAGS="${CFLAGS} -O3"
    - export CXXFLAGS="${CXXFLAGS} -O3"
    - {{ PYTHON }} -m pip install . --no-deps --no-build-isolation --no-cache-dir -vvv
  run_exports:
    - {{ pin_subpackage(name, max_pin="x.x") }}

requirements:
  build:
    - {{ compiler('c') }}
    - {{ compiler('cxx') }}
    - cmake >=3.20
    - make
    - pkg-config
  host:
    - python
    - pip
    - scikit-build-core
    - cython >=3.0
    - scoring-matrices >=0.3.2
    - zlib
  run:
    - python
    - archspec >=0.2
    - scoring-matrices >=0.3.2

test:
  imports:
    - pytantan

about:
  home: "https://github.com/althonos/pytantan"
  dev_url: "https://github.com/althonos/pytantan"
  summary: "Cython bindings and Python interface to Tantan, a fast method for identifying repeats in DNA and protein sequences."
  license: "GPL-3.0-or-later"
  license_family: GPL3
  license_file:
    - COPYING
    - vendor/tantan/COPYING.txt
  doc_url: "https://pytantan.readthedocs.io/en/latest"

extra:
  recipe-maintainers:
    - althonos
  additional-platforms:
    - linux-aarch64
    - osx-arm64
