recipe_sha: 1f27a21b2f4143d04dce0a1774e00273fd66de8d5a0d68a49a2cb9b8ec1b07a7  # The hash of the recipe's meta.yaml at which this recipe failed to build.
skiplist: true # Set to true to skiplist this recipe so that it will be ignored as long as its latest commit is the one given above.
log: |2-
   '[' Linux = Darwin ']'
   sqlite=SQLITE=true
   make CC=/opt/conda/conda-bld/augustus_1734294070029/_build_env/bin/x86_64-conda-linux-gnu-cc CXX=/opt/conda/conda-bld/augustus_1734294070029/_build_env/bin/x86_64-conda-linux-gnu-c BAMTOOLS_CC=/opt/conda/conda-bld/augustus_1734294070029/_build_env/bin/x86_64-conda-linux-gnu-cc BAMTOOLS_CXX=/opt/conda/conda-bld/augustus_1734294070029/_build_env/bin/x86_64-conda-linux-gnu-c BAMTOOLS=/opt/conda/conda-bld/augustus_1734294070029/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac COMPGENPRED=true SQLITE=true
  mkdir -p bin
  cd src && make
  make[1]: Entering directory '$SRC_DIR/src'
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o genbank.o genbank.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o properties.o properties.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o pp_profile.o pp_profile.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o pp_hitseq.o pp_hitseq.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o pp_scoring.o pp_scoring.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o statemodel.o statemodel.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o namgene.o namgene.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o types.o types.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o gene.o gene.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o evaluation.o evaluation.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o motif.o motif.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o geneticcode.o geneticcode.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o hints.o hints.cc -I../include -I$PREFIX/include/lpsolve
  hints.cc: In function 'std::istream& operator>>(std::istream&, Feature&)':
  hints.cc:82:16: warning: 'char* strncpy(char*, const char*, size_t)' output may be truncated copying 1014 bytes from a string of length 1023 [-Wstringop-truncation]
     82 |         strncpy(copybuff, buff, 1014);
        |         ~~~~~~~^~~~~~~~~~~~~~~~~~~~~~
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o extrinsicinfo.o extrinsicinfo.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o projectio.o projectio.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o intronmodel.o intronmodel.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o exonmodel.o exonmodel.cc -I../include -I$PREFIX/include/lpsolve
  exonmodel.cc: In static member function 'static void ExonModel::readAllParameters()':
  exonmodel.cc:671:28: warning: '%d' directive writing between 1 and 10 bytes into a region of size 5 [-Wformat-overflow=]
    671 |       sprintf(zusString, "[%d]", idx1);
        |                            ^~
  exonmodel.cc:671:26: note: directive argument in the range [1, 2147483647]
    671 |       sprintf(zusString, "[%d]", idx1);
        |                          ^~~~~~
  exonmodel.cc:671:14: note: 'sprintf' output between 4 and 13 bytes into a destination of size 6
    671 |       sprintf(zusString, "[%d]", idx1);
        |       ~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o igenicmodel.o igenicmodel.cc -I../include -I$PREFIX/include/lpsolve
  igenicmodel.cc: In static member function 'static void IGenicModel::readAllParameters()':
  igenicmodel.cc:162:32: warning: '%d' directive writing between 1 and 10 bytes into a region of size 5 [-Wformat-overflow=]
    162 |           sprintf(zusString, "[%d]", idx1);
        |                                ^~
  igenicmodel.cc:162:30: note: directive argument in the range [1, 2147483647]
    162 |           sprintf(zusString, "[%d]", idx1);
        |                              ^~~~~~
  igenicmodel.cc:162:18: note: 'sprintf' output between 4 and 13 bytes into a destination of size 6
    162 |           sprintf(zusString, "[%d]", idx1);
        |           ~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o utrmodel.o utrmodel.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o merkmal.o merkmal.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o vitmatrix.o vitmatrix.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o lldouble.o lldouble.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o mea.o mea.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o graph.o graph.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o meaPath.o meaPath.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o exoncand.o exoncand.cc -I../include -I$PREFIX/include/lpsolve
  $BUILD_PREFIX/bin/x86_64-conda-linux-gnu-c -c -Wall -Wno-sign-compare -pedantic -g -ggdb -O3 -fvisibility-inlines-hidden -fmessage-length=0 -march=nocona -mtune=haswell -ftree-vectorize -fPIC -fstack-protector-strong -fno-plt -O2 -ffunction-sections -pipe -isystem $PREFIX/include -fdebug-prefix-map=$SRC_DIR=/usr/local/src/conda/augustus-3.3.3 -fdebug-prefix-map=$PREFIX=/usr/local/src/conda-prefix -std=c11  -DUSE_BOOST -I$PREFIX/include/bamtools -std=c11 -DCOMPGENEPRED -DSQLITE -o randseqaccess.o randseqaccess.cc -I../include -I$PREFIX/include/lpsolve
  In file included from ../include/randseqaccess.hh:25,
                   from randseqaccess.cc:10:
  ../include/sqliteDB.hh:93:29: error: 'uint64_t' has not been declared
     93 |     void bindInt64(int idx, uint64_t x);
        |                             ^~~~~~~~
  ../include/sqliteDB.hh:102:12: error: 'uint64_t' does not name a type
    102 |     inline uint64_t int64Column(int colNum){return (uint64_t)sqlite3_column_int64(stmt,colNum);}
        |            ^~~~~~~~
  ../include/sqliteDB.hh:14:1: note: 'uint64_t' is defined in header '<cstdint>'; did you forget to '#include <cstdint>'?
     13 | #include <sqlite3.h>
     |#include <cstdint>
     14 |
  randseqaccess.cc: In member function 'virtual AnnoSequence* SQLiteAccess::getSeq(std::string, std::string, int, int, Strand)':
  randseqaccess.cc:772:47: error: 'class Statement' has no member named 'int64Column'; did you mean 'intColumn'?
    772 |             file_start = (std::streampos)stmt.int64Column(2);
        |                                               ^~~~~~~~~~~
        |                                               intColumn
  make[1]: *** [Makefile:72: randseqaccess.o] Error 1
  make[1]: Leaving directory '$SRC_DIR/src'
  make: *** [Makefile:8: all] Error 2
  Traceback (most recent call last):
    File "/opt/conda/lib/python3.10/site-packages/conda_build/build.py", line 2558, in build
      utils.check_call_env(
    File "/opt/conda/lib/python3.10/site-packages/conda_build/utils.py", line 404, in check_call_env
      return _func_defaulting_env_to_os_environ("call", *popenargs, **kwargs)
    File "/opt/conda/lib/python3.10/site-packages/conda_build/utils.py", line 380, in _func_defaulting_env_to_os_environ
      raise subprocess.CalledProcessError(proc.returncode, _args)
  subprocess.CalledProcessError: Command '['/bin/bash', '-o', 'errexit', '/opt/conda/conda-bld/augustus_1734294070029/work/conda_build.sh']' returned non-zero exit status 2.

  The above exception was the direct cause of the following exception:

  Traceback (most recent call last):
    File "/opt/conda/bin/conda-build", line 11, in <module>
      sys.exit(execute())
    File "/opt/conda/lib/python3.10/site-packages/conda_build/cli/main_build.py", line 589, in execute
      api.build(
    File "/opt/conda/lib/python3.10/site-packages/conda_build/api.py", line 209, in build
      return build_tree(
    File "/opt/conda/lib/python3.10/site-packages/conda_build/build.py", line 3712, in build_tree
      packages_from_this = build(
    File "/opt/conda/lib/python3.10/site-packages/conda_build/build.py", line 2566, in build
      raise BuildScriptException(str(exc), caused_by=exc) from exc
  conda_build.exceptions.BuildScriptException: Command '['/bin/bash', '-o', 'errexit', '/opt/conda/conda-bld/augustus_1734294070029/work/conda_build.sh']' returned non-zero exit status 2.
# Last 100 lines of the build log.
category: |-
  compiler error
