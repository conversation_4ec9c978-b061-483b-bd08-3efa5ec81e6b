recipe_sha: 1f27a21b2f4143d04dce0a1774e00273fd66de8d5a0d68a49a2cb9b8ec1b07a7  # The hash of the recipe's meta.yaml at which this recipe failed to build.
skiplist: true # Set to true to skiplist this recipe so that it will be ignored as long as its latest commit is the one given above.
log: |2-
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_build/environ.py", line 1307, in install_actions
      txn = solver.solve_for_transaction(prune=False, ignore_pinned=False)
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda/core/solve.py", line 152, in solve_for_transaction
      unlink_precs, link_precs = self.solve_for_diff(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda/core/solve.py", line 221, in solve_for_diff
      final_precs = self.solve_final_state(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_libmamba_solver/solver.py", line 223, in solve_final_state
      out_state = self._solving_loop(in_state, out_state, index)
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_libmamba_solver/solver.py", line 303, in _solving_loop
      solved = self._solve_attempt(in_state, out_state, index, attempt=attempt)
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_libmamba_solver/solver.py", line 414, in _solve_attempt
      new_conflicts = self._maybe_raise_for_problems(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_libmamba_solver/solver.py", line 712, in _maybe_raise_for_problems
      self._maybe_raise_for_conda_build(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_libmamba_solver/solver.py", line 805, in _maybe_raise_for_conda_build
      raise exc
  conda_libmamba_solver.conda_build_exceptions.ExplainedDependencyNeedsBuildingError: Unsatisfiable dependencies for platform osx-64: {MatchSpec("libzlib[version='>=1.3.1,<2.0a0']"), MatchSpec("libsqlite==3.47.2=hdb6dae5_0")}
  Encountered problems while solving:
    - package libsqlite-3.47.2-hdb6dae5_0 requires libzlib >=1.3.1,<2.0a0, but none of the providers can be installed

  Could not solve for environment specs
  The following packages are incompatible
   cdbtools is installable with the potential options
     cdbtools 0.99 would require
       zlib >=1.2.11,<1.3.0a0 , which can be installed;
     cdbtools 0.99 would require
       zlib >=1.2.13,<1.3.0a0 , which can be installed;
     cdbtools 0.99 would require
        libzlib >=1.2.13,<1.3.0a0 , which can be installed;
   libsqlite >=3.47.2,<4.0a0  is not installable because it requires
      libzlib >=1.3.1,<2.0a0  but there are no viable options
         libzlib 1.3.1 would require
           zlib 1.3.1 *_0, which conflicts with any installable versions previously reported;
         libzlib 1.3.1 would require
           zlib 1.3.1 *_1, which conflicts with any installable versions previously reported;
         libzlib 1.3.1 would require
            zlib 1.3.1 *_2, which conflicts with any installable versions previously reported.

  During handling of the above exception, another exception occurred:

  Traceback (most recent call last):
    File "/opt/mambaforge/envs/bioconda/bin/conda-build", line 11, in <module>
      sys.exit(execute())
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_build/cli/main_build.py", line 589, in execute
      api.build(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_build/api.py", line 209, in build
      return build_tree(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_build/build.py", line 3712, in build_tree
      packages_from_this = build(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_build/build.py", line 2439, in build
      create_build_envs(top_level_pkg, notest)
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_build/build.py", line 2277, in create_build_envs
      raise e
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_build/build.py", line 2250, in create_build_envs
      environ.get_package_records(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_build/environ.py", line 938, in get_install_actions
      precs = get_package_records(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_build/environ.py", line 938, in get_install_actions
      precs = get_package_records(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_build/environ.py", line 938, in get_install_actions
      precs = get_package_records(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_build/environ.py", line 891, in get_install_actions
      _actions = _install_actions(prefix, index, specs, subdir=subdir)
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_build/environ.py", line 1307, in install_actions
      txn = solver.solve_for_transaction(prune=False, ignore_pinned=False)
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda/core/solve.py", line 152, in solve_for_transaction
      unlink_precs, link_precs = self.solve_for_diff(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda/core/solve.py", line 221, in solve_for_diff
      final_precs = self.solve_final_state(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_libmamba_solver/solver.py", line 223, in solve_final_state
      out_state = self._solving_loop(in_state, out_state, index)
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_libmamba_solver/solver.py", line 303, in _solving_loop
      solved = self._solve_attempt(in_state, out_state, index, attempt=attempt)
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_libmamba_solver/solver.py", line 414, in _solve_attempt
      new_conflicts = self._maybe_raise_for_problems(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_libmamba_solver/solver.py", line 712, in _maybe_raise_for_problems
      self._maybe_raise_for_conda_build(
    File "/opt/mambaforge/envs/bioconda/lib/python3.10/site-packages/conda_libmamba_solver/solver.py", line 805, in _maybe_raise_for_conda_build
      raise exc
  conda_libmamba_solver.conda_build_exceptions.ExplainedDependencyNeedsBuildingError: Unsatisfiable dependencies for platform osx-64: {MatchSpec("libzlib[version='>=1.3.1,<2.0a0']"), MatchSpec("libsqlite==3.47.2=hdb6dae5_0")}
  Encountered problems while solving:
    - package libsqlite-3.47.2-hdb6dae5_0 requires libzlib >=1.3.1,<2.0a0, but none of the providers can be installed

  Could not solve for environment specs
  The following packages are incompatible
   cdbtools is installable with the potential options
     cdbtools 0.99 would require
       zlib >=1.2.11,<1.3.0a0 , which can be installed;
     cdbtools 0.99 would require
       zlib >=1.2.13,<1.3.0a0 , which can be installed;
     cdbtools 0.99 would require
        libzlib >=1.2.13,<1.3.0a0 , which can be installed;
   libsqlite >=3.47.2,<4.0a0  is not installable because it requires
      libzlib >=1.3.1,<2.0a0  but there are no viable options
         libzlib 1.3.1 would require
           zlib 1.3.1 *_0, which conflicts with any installable versions previously reported;
         libzlib 1.3.1 would require
           zlib 1.3.1 *_1, which conflicts with any installable versions previously reported;
         libzlib 1.3.1 would require
            zlib 1.3.1 *_2, which conflicts with any installable versions previously reported.
# Last 100 lines of the build log.
category: |-
  compiler error
