--- auxprogs/utrrnaseq/Debug/makefile.old	2019-04-12 23:55:19.432385725 -0500
+++ auxprogs/utrrnaseq/Debug/makefile	2019-04-13 00:10:03.252201299 -0500
@@ -44,14 +44,16 @@
 utrrnaseq: $(OBJS) $(USER_OBJS)
 	@echo 'Building target: $@'
 	@echo 'Invoking: GCC C++ Linker'
-	g++  -o "utrrnaseq" $(OBJS) $(USER_OBJS) $(LIBS)
+	$(CXX) -o "utrrnaseq" $(OBJS) $(USER_OBJS) $(LIBS)
 	@echo 'Finished building target: $@'
 	@echo ' '
+	cp utrrnaseq ../../../bin/utrrnaseq
 
 # Other Targets
 clean:
 	-$(RM) $(CC_DEPS)$(C++_DEPS)$(EXECUTABLES)$(C_UPPER_DEPS)$(CXX_DEPS)$(OBJS)$(CPP_DEPS)$(C_DEPS) utrrnaseq
 	-@echo ' '
+	rm ../../../bin/utrrnaseq
 
 .PHONY: all clean dependents
 
