--- auxprogs/compileSpliceCands/Makefile.old	2019-04-12 23:52:15.797425115 -0500
+++ auxprogs/compileSpliceCands/Makefile	2019-04-12 23:54:15.845399362 -0500
@@ -1,8 +1,8 @@
 compileSpliceCands : compileSpliceCands.o list.h list.o
-	gcc $(LDFLAGS) -o compileSpliceCands compileSpliceCands.o list.o;
-#	cp compileSpliceCands ../../bin
-compileSpliceCands.o : compileSpliceCands.c 
-	gcc -Wall -pedantic -ansi $(CPPFLAGS) -c compileSpliceCands.c 
+	$(CC) -o compileSpliceCands compileSpliceCands.o list.o;
+	cp compileSpliceCands ../../bin/compileSpliceCands
+compileSpliceCands.o : compileSpliceCands.c
+	$(CC) -Wall -pedantic -ansi $(CPPFLAGS) -c compileSpliceCands.c
 
 all : compileSpliceCands
 
