--- auxprogs/bam2wig/Makefile.orig	2019-09-13 08:32:52.000000000 -0400
+++ auxprogs/bam2wig/Makefile	2019-09-24 09:28:00.000000000 -0400
@@ -7,7 +7,7 @@
 #
 
 ifndef TOOLDIR
-	TOOLDIR=$(HOME)/tools
+	TOOLDIR=${PREFIX}/include
 # replace with your own parent directory of samtools, htslib, bcftools
 # if TOOLDIR is not specified as environment variable
 endif
@@ -18,14 +18,13 @@
 SAMTOOLS=$(TOOLDIR)/samtools
 HTSLIB=$(TOOLDIR)/htslib
 BCFTOOLS=$(TOOLDIR)/bcftools
-INCLUDES=-I$(SAMTOOLS) -I. -I$(HTSLIB) -I$(BCFTOOLS)
+INCLUDES=-I$(SAMTOOLS) -I. -I${PREFIX}/include/bam -I${PREFIX}/include
 VPATH=$(SAMTOOLS)
-LIBS=$(SAMTOOLS)/libbam.a $(HTSLIB)/libhts.a -lcurses -lm -lz -lpthread -lcurl -lssl -lcrypto
+LIBS=-lbam -lhts -lncurses -lm -lz -lpthread -lcurl -lbz2 -llzma -L${PREFIX}/lib/
 CFLAGS=-Wall -O2 $(INCLUDES)
-CC=gcc
 
 $(PROGRAM) : bam2wig.o
-	$(CC) $(CFLAGS) $^ -o $@ $(LIBS) -lbz2 -llzma
+	$(CC) $(CFLAGS) $^ -o $@ $(LIBS)
 	mkdir -p ../../bin
 	cp bam2wig ../../bin/bam2wig
 
