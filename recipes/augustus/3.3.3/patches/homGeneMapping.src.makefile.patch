--- auxprogs/homGeneMapping/src/Makefile.orig	2019-09-13 08:32:52.000000000 -0400
+++ auxprogs/homGeneMapping/src/Makefile	2019-09-24 11:49:14.000000000 -0400
@@ -7,14 +7,12 @@
 # database access for retrieval of hints
 # SQLITE = true
 
-CC      = g++
-
 # Notes: - "-Wno-sign-compare" eliminates a high number of warnings (see footnote below). Please adopt
 #          a strict signed-only usage strategy to avoid mistakes since we are not warned about this.
 #        - The order of object files in $(OBJS) IS IMPORTANT (see lldouble.hh)
 CFLAGS := -Wall -Wno-sign-compare -ansi -pedantic -std=c++0x -pthread -O2 ${CFLAGS} # -DDEBUG -g -ggdb -pg
 
-INCLS	= -I../include
+INCLS	= -I../include -I${PREFIX}/include
 LIBS	=
 OBJS	= gene.o genome.o
 
@@ -25,9 +23,9 @@
 
 ifdef SQLITE
 INCLS	+=           # add the sqlite include path here, if sqlite is not installed system-wide
-LIBS    += -lsqlite3 # add the sqlite library path here, if sqlite is not install system-wide
+LIBS    += -lsqlite3 -L${PREFIX}/lib/ # add the sqlite library path here, if sqlite is not install system-wide
 OBJS    += sqliteDB.o
-CFLAGS  += -DSQLITE
+CFLAGS  += -DSQLITE -ldl
 endif
 
 all: homGeneMapping
@@ -36,10 +34,10 @@
 .SUFFIXES: .cc .o .so
 
 .cc.o:
-	$(CC) -c $(CFLAGS) $(CXXFLAGS) $(CPPFLAGS) $(LDFLAGS) -o $@ $< $(INCLS)
+	$(CXX) -c $(CFLAGS) $(CXXFLAGS) $(CPPFLAGS) $(LDFLAGS) -o $@ $< $(INCLS)
 
 homGeneMapping: main.cc $(OBJS)
-	$(CC) $(CFLAGS) $(CPPFLAGS) -o $@ $^ $(INCLS)  $(LIBS)
+	$(CXX) $(CFLAGS) $(CPPFLAGS) -o $@ $^ $(INCLS)  $(LIBS)
 	mkdir -p ../../../bin/
 	cp homGeneMapping ../../../bin/homGeneMapping
 
