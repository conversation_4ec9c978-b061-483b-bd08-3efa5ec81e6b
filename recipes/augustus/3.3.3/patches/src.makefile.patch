--- src/Makefile.old	2019-03-27 13:46:51.646006063 -0500
+++ src/Makefile	2019-03-27 13:47:26.077003947 -0500
@@ -34,7 +34,7 @@
 	OBJS += parser/parse.o scanner/lex.o genomicMSA.o geneMSA.o contTimeMC.o compgenepred.o phylotree.o orthograph.o orthoexon.o alignment.o speciesgraph.o codonMSA.o train_logReg_param.o
 	LIBS += -lgsl -lgslcblas # for matrix exponentiation that is required in comparative gene finding
 	LIBS += -llpsolve55 -lcolamd -ldl # for mixed integer linear programming (alignment.cc)
-	INCLS += -I/usr/include/lpsolve
+	INCLS += -I${PREFIX}/include/lpsolve
 endif
 DUMOBJS = dummy.o
 TOBJS	= commontrain.o igenictrain.o introntrain.o exontrain.o utrtrain.o # contentmodel.o baumwelch.o
