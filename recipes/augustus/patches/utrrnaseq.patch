diff --git a/auxprogs/utrrnaseq/Makefile b/auxprogs/utrrnaseq/Makefile
index 134c5c5..63fa103 100644
--- a/auxprogs/utrrnaseq/Makefile
+++ b/auxprogs/utrrnaseq/Makefile
@@ -3,7 +3,7 @@
 include ../../common.mk
 
 CXX ?= g++
-CXXFLAGS := -Wall -O0 -pedantic -fmessage-length=0 ${CXXFLAGS}
+CXXFLAGS := -Wall -O3 -pedantic -fmessage-length=0 -std=c++14 ${CXXFLAGS}
 INCLS   += $(INCLUDE_PATH_BOOST) # set boost include path in INCLUDE_PATH_BOOST, if boost is not installed system-wide
 LDFLAGS += $(LIBRARY_PATH_BOOST) # set boost library path in LIBRARY_PATH_BOOST, if boost is not installed system-wide
 
@@ -29,6 +29,7 @@ utrrnaseq: $(OBJS)
 	$(CXX) $(CPPFLAGS) $(CXXFLAGS) $(LDFLAGS) -o $@ $^ $(LIBS)
 	@echo 'Finished building target: $@'
 	@echo ' '
+	cp -f utrrnaseq ../../bin/utrrnaseq
 
 $(OBJS): $(OBJ_DIR)/%.o: $(SRC_DIR)/%.cpp
 	@echo 'Building file: $<'
@@ -55,5 +56,6 @@ clean_test:
 clean: clean_test
 	rm -rf $(OBJS) $(DEPS) $(OBJ_DIR) utrrnaseq
 	@echo ' '
+	rm ../../bin/utrrnaseq
 
 -include $(DEPS)
