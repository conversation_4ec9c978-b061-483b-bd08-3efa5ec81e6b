#!/usr/bin/env python3
"""
Test different logging scenarios for enhanced memory management.
"""

def show_logging_scenarios():
    """Show what logging appears under different command scenarios."""
    
    print("Enhanced Memory Management Logging Scenarios")
    print("=" * 55)
    
    scenarios = [
        {
            "name": "Default (No Flags)",
            "command": "funannotate2 predict -f genome.fa -p params.json -o output",
            "flags": "None",
            "logging": [
                "• Standard tool execution",
                "• Basic error messages if tools fail",
                "• Enhanced OOM detection (NEW!)",
                "• Suggests using --monitor-memory if OOM detected"
            ]
        },
        {
            "name": "Memory Monitoring Only",
            "command": "funannotate2 predict -f genome.fa -p params.json -o output --monitor-memory",
            "flags": "--monitor-memory",
            "logging": [
                "• 'Memory monitoring enabled - using enhanced error tracking'",
                "• 'Processing contig scaffold_1 (length: 102,000,000 bp)'",
                "• 'snap memory prediction for scaffold_1: 10890.9 MB'",
                "• 'WARNING: snap predicted to use 10890.9 MB but only 1536.0 MB available'",
                "• 'LIKELY OOM ERROR: snap on contig scaffold_1 (length: 102,000,000 bp, predicted: 10890.9 MB)'",
                "• 'Failed tools for scaffold_1: snap'",
                "• 'Successfully ran tools for scaffold_1: augustus, glimmerhmm, genemark'"
            ]
        },
        {
            "name": "Full Memory Management",
            "command": "funannotate2 predict -f genome.fa -p params.json -o output --monitor-memory --memory-limit 8",
            "flags": "--monitor-memory --memory-limit 8",
            "logging": [
                "• 'Using memory-aware job scheduling to prevent OOM errors'",
                "• 'Analyzing memory requirements for all contigs...'",
                "• 'Categorized jobs: 150 low-memory, 5 high-memory'",
                "• 'Running 150 low-memory jobs in parallel (up to 8 CPUs)'",
                "• 'Running 5 high-memory jobs sequentially to prevent OOM'",
                "• 'Processing high-memory contig: scaffold_1'",
                "• All the memory monitoring logs from above",
                "• 'Retrying 1 failed jobs with minimal resources' (if needed)"
            ]
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['name']}")
        print("-" * 40)
        print(f"Command: {scenario['command']}")
        print(f"Flags: {scenario['flags']}")
        print("Logging Output:")
        for log_item in scenario['logging']:
            print(f"  {log_item}")
    
    print(f"\n" + "=" * 55)
    print("Key Points:")
    print("=" * 55)
    print("✅ Enhanced OOM detection works with ANY command (no flags needed)")
    print("✅ Memory predictions require --monitor-memory flag")
    print("✅ Full job scheduling requires --monitor-memory AND --memory-limit")
    print("✅ All logging goes to BOTH console AND log file")
    print("✅ Log file location: {output_dir}/logs/funannotate-predict.log")
    
    print(f"\nTo see the enhanced logging, try:")
    print("funannotate2 predict -f genome.fa -p params.json -o output --monitor-memory")

if __name__ == "__main__":
    show_logging_scenarios()
