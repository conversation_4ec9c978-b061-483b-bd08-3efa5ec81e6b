#!/usr/bin/env python3
"""
Analyze second user's memory monitoring data and compare with our updated predictions.
"""

import json
import statistics
import math

def calculate_correlation(x_values, y_values):
    """Calculate Pearson correlation coefficient."""
    if len(x_values) != len(y_values) or len(x_values) < 2:
        return 0.0
    
    n = len(x_values)
    sum_x = sum(x_values)
    sum_y = sum(y_values)
    sum_xy = sum(x * y for x, y in zip(x_values, y_values))
    sum_x2 = sum(x * x for x in x_values)
    sum_y2 = sum(y * y for y in y_values)
    
    numerator = n * sum_xy - sum_x * sum_y
    denominator = math.sqrt((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y))
    
    if denominator == 0:
        return 0.0
    
    return numerator / denominator

def linear_regression(x_values, y_values):
    """Calculate linear regression slope and intercept."""
    if len(x_values) != len(y_values) or len(x_values) < 2:
        return 0.0, 0.0
    
    n = len(x_values)
    sum_x = sum(x_values)
    sum_y = sum(y_values)
    sum_xy = sum(x * y for x, y in zip(x_values, y_values))
    sum_x2 = sum(x * x for x in x_values)
    
    denominator = n * sum_x2 - sum_x * sum_x
    if denominator == 0:
        return 0.0, statistics.mean(y_values)
    
    slope = (n * sum_xy - sum_x * sum_y) / denominator
    intercept = (sum_y - slope * sum_x) / n
    
    return slope, intercept

def analyze_user2_data():
    """Analyze the second user's memory monitoring data."""
    
    jsonl_file = "/Users/<USER>/Downloads/memory-monitoring-2.jsonl"
    
    # Read all records
    records = []
    with open(jsonl_file, 'r') as f:
        for line in f:
            try:
                record = json.loads(line.strip())
                # Only include records with valid contig length data
                if (record.get('contig_length_estimate') is not None and 
                    record.get('tool_name')):
                    records.append(record)
            except json.JSONDecodeError:
                continue
    
    print(f"Loaded {len(records)} valid records with contig length data")
    
    # Group by tool
    tools = {}
    for record in records:
        tool = record['tool_name']
        if tool not in tools:
            tools[tool] = []
        tools[tool].append(record)
    
    print(f"Found tools: {list(tools.keys())}")
    
    # Our current prediction values (updated from first user)
    current_base_memory = {
        "snap": 0.0,
        "augustus": 35.8,
        "glimmerhmm": 6.4,
        "genemark": 20.0,
    }
    
    current_scaling_factor = {
        "snap": 93.3,
        "augustus": 12.4,
        "glimmerhmm": 6.6,
        "genemark": 50.0,
    }
    
    print("\n" + "="*80)
    print("USER 2 MEMORY ANALYSIS")
    print("="*80)
    
    # Analyze each tool
    results = {}
    for tool, tool_records in tools.items():
        if len(tool_records) < 5:
            print(f"Skipping {tool}: insufficient data ({len(tool_records)} records)")
            continue
        
        print(f"\n{tool.upper()}:")
        print(f"  Sample size: {len(tool_records)} contigs")
        
        # Extract data
        contig_lengths = [r['contig_length_estimate'] for r in tool_records]
        peak_memories = [r['memory_stats']['peak_rss_mb'] for r in tool_records]
        
        # Convert to Mbp for easier interpretation
        contig_lengths_mbp = [length / 1_000_000 for length in contig_lengths]
        
        # Calculate statistics
        min_length = min(contig_lengths)
        max_length = max(contig_lengths)
        avg_length = statistics.mean(contig_lengths)
        median_length = statistics.median(contig_lengths)
        
        min_memory = min(peak_memories)
        max_memory = max(peak_memories)
        avg_memory = statistics.mean(peak_memories)
        median_memory = statistics.median(peak_memories)
        
        print(f"  Contig length range: {min_length:,} - {max_length:,} bp")
        print(f"  Median contig length: {median_length:,} bp")
        print(f"  Memory usage range: {min_memory:.1f} - {max_memory:.1f} MB")
        print(f"  Median memory usage: {median_memory:.1f} MB")
        
        # Calculate correlation and linear regression
        correlation = calculate_correlation(contig_lengths_mbp, peak_memories)
        slope, intercept = linear_regression(contig_lengths_mbp, peak_memories)
        
        print(f"  Correlation: {correlation:.3f}")
        print(f"  Linear model: Memory = {slope:.1f} * Size_Mbp + {intercept:.1f}")
        
        # Compare with our current predictions
        current_base = current_base_memory.get(tool, 0)
        current_scaling = current_scaling_factor.get(tool, 0)
        
        # Calculate prediction accuracy for median contig
        predicted_memory = current_base + (current_scaling * median_length / 1_000_000)
        prediction_error = abs(predicted_memory - median_memory) / median_memory * 100
        
        print(f"  Current prediction for median: {predicted_memory:.1f} MB")
        print(f"  Actual median usage: {median_memory:.1f} MB")
        print(f"  Prediction error: {prediction_error:.1f}%")
        
        if prediction_error < 20:
            print(f"  ✅ GOOD ACCURACY (< 20% error)")
        elif prediction_error < 50:
            print(f"  ⚠️  MODERATE ERROR (20-50% error)")
        else:
            print(f"  ❌ POOR ACCURACY (> 50% error)")
        
        results[tool] = {
            'sample_size': len(tool_records),
            'median_length': median_length,
            'median_memory': median_memory,
            'max_memory': max_memory,
            'correlation': correlation,
            'observed_slope': slope,
            'observed_intercept': intercept,
            'prediction_error': prediction_error,
            'current_base': current_base,
            'current_scaling': current_scaling
        }
    
    # Check for memory spikes that could cause OOM
    print(f"\n" + "="*80)
    print("MEMORY SPIKE ANALYSIS")
    print("="*80)
    
    all_peak_memories = []
    for tool_records in tools.values():
        for record in tool_records:
            all_peak_memories.append(record['memory_stats']['peak_rss_mb'])
    
    if all_peak_memories:
        max_observed = max(all_peak_memories)
        avg_observed = statistics.mean(all_peak_memories)
        p95_observed = sorted(all_peak_memories)[int(len(all_peak_memories) * 0.95)]
        
        print(f"Maximum memory usage observed: {max_observed:.1f} MB")
        print(f"Average memory usage: {avg_observed:.1f} MB")
        print(f"95th percentile memory usage: {p95_observed:.1f} MB")
        
        # Check for concerning spikes
        if max_observed > 1000:  # > 1 GB
            print(f"⚠️  HIGH MEMORY USAGE DETECTED: {max_observed:.1f} MB")
            print(f"   This could cause OOM errors on systems with limited memory")
        
        # Find the record with maximum memory usage
        max_record = None
        for tool_records in tools.values():
            for record in tool_records:
                if record['memory_stats']['peak_rss_mb'] == max_observed:
                    max_record = record
                    break
            if max_record:
                break
        
        if max_record:
            print(f"   Tool: {max_record['tool_name']}")
            print(f"   Contig: {max_record['contig_name']}")
            print(f"   Contig length: {max_record['contig_length_estimate']:,} bp")
    
    return results

def compare_with_user1():
    """Compare User 2 data with User 1 patterns."""
    print(f"\n" + "="*80)
    print("COMPARISON WITH USER 1 DATA")
    print("="*80)
    
    # User 1 linear models (from previous analysis)
    user1_models = {
        "snap": {"slope": 93.3, "intercept": -2.1},
        "augustus": {"slope": 12.4, "intercept": 35.8},
        "glimmerhmm": {"slope": 6.6, "intercept": 6.4}
    }
    
    print("User 1 models (from 4,083 measurements):")
    for tool, model in user1_models.items():
        print(f"  {tool}: Memory = {model['slope']:.1f} * Size_Mbp + {model['intercept']:.1f}")

def main():
    try:
        results = analyze_user2_data()
        compare_with_user1()
        
        print(f"\n" + "="*80)
        print("RECOMMENDATIONS")
        print("="*80)
        
        needs_update = False
        for tool, data in results.items():
            if data['prediction_error'] > 20:
                needs_update = True
                print(f"{tool}: Consider updating (error: {data['prediction_error']:.1f}%)")
                print(f"  Observed model: Memory = {data['observed_slope']:.1f} * Size_Mbp + {data['observed_intercept']:.1f}")
        
        if not needs_update:
            print("✅ Current predictions appear accurate for User 2 data")
        
    except Exception as e:
        print(f"Error analyzing data: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
