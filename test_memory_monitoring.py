#!/usr/bin/env python3
"""
Test script to demonstrate memory monitoring functionality for funannotate2 ab initio predictions.

This script shows how to:
1. Predict memory usage based on contig length
2. Monitor actual memory usage of subprocess calls
3. Suggest optimal CPU allocation based on memory constraints
4. Generate memory usage reports

Usage:
    python test_memory_monitoring.py
"""

import os
import sys
import tempfile
import time

# Add funannotate2 to path for testing
sys.path.insert(0, '/Users/<USER>/software/funannotate2')

try:
    from funannotate2.memory import (
        MemoryMonitor,
        get_contig_length,
        predict_memory_usage,
        estimate_total_memory_usage,
        suggest_cpu_allocation,
        get_system_memory_info,
        format_memory_report
    )
    print("✓ Successfully imported memory monitoring modules")
except ImportError as e:
    print(f"✗ Failed to import memory modules: {e}")
    sys.exit(1)

def create_test_contig(length_bp, filename):
    """Create a test FASTA file with specified length."""
    with open(filename, 'w') as f:
        f.write(">test_contig\n")
        # Write sequence in 80-character lines
        sequence = "ATCG" * (length_bp // 4)
        for i in range(0, len(sequence), 80):
            f.write(sequence[i:i+80] + "\n")
    return filename

def test_memory_prediction():
    """Test memory prediction functionality."""
    print("\n=== Testing Memory Prediction ===")
    
    # Test different contig lengths
    test_lengths = [100000, 1000000, 10000000, 50000000]  # 100kb, 1Mb, 10Mb, 50Mb
    tools = ['snap', 'augustus', 'glimmerhmm', 'genemark']
    
    print(f"{'Contig Size':<12} {'Tool':<12} {'Predicted (MB)':<15} {'With Margin (MB)':<18}")
    print("-" * 60)
    
    for length in test_lengths:
        for tool in tools:
            prediction = predict_memory_usage(tool, length)
            print(f"{length/1e6:>8.1f} Mb {tool:<12} {prediction['predicted_peak_mb']:>10.1f} {prediction['predicted_peak_with_margin_mb']:>13.1f}")

def test_system_memory():
    """Test system memory information."""
    print("\n=== Testing System Memory Info ===")
    
    memory_info = get_system_memory_info()
    if 'error' in memory_info:
        print(f"Error getting memory info: {memory_info['error']}")
        return
    
    print(f"Total memory: {memory_info['total_gb']:.1f} GB")
    print(f"Available memory: {memory_info['available_gb']:.1f} GB")
    print(f"Used memory: {memory_info['used_gb']:.1f} GB ({memory_info['percent_used']:.1f}%)")
    print(f"Free memory: {memory_info['free_gb']:.1f} GB")

def test_cpu_allocation():
    """Test CPU allocation suggestions."""
    print("\n=== Testing CPU Allocation Suggestions ===")
    
    # Test scenarios
    scenarios = [
        (500, 8, 8),    # 500MB per process, 8GB available, 8 CPUs
        (1000, 8, 8),   # 1GB per process, 8GB available, 8 CPUs  
        (2000, 8, 8),   # 2GB per process, 8GB available, 8 CPUs
        (500, 4, 16),   # 500MB per process, 4GB available, 16 CPUs
    ]
    
    print(f"{'Memory/Process':<15} {'Available GB':<12} {'Max CPUs':<10} {'Suggested':<10} {'Limited?':<10}")
    print("-" * 70)
    
    for memory_mb, available_gb, max_cpus in scenarios:
        suggestion = suggest_cpu_allocation(memory_mb, available_gb, max_cpus)
        limited = "Yes" if suggestion['memory_limited'] else "No"
        print(f"{memory_mb:>10} MB {available_gb:>8} GB {max_cpus:>8} {suggestion['suggested_cpus']:>8} {limited:<10}")

def test_total_estimation():
    """Test total memory estimation for multiple contigs."""
    print("\n=== Testing Total Memory Estimation ===")
    
    # Create temporary test contigs
    with tempfile.TemporaryDirectory() as tmpdir:
        contigs = []
        contig_sizes = [1000000, 5000000, 2000000]  # 1Mb, 5Mb, 2Mb
        
        for i, size in enumerate(contig_sizes):
            contig_file = os.path.join(tmpdir, f"contig_{i+1}.fasta")
            create_test_contig(size, contig_file)
            contigs.append(contig_file)
        
        tools = ['snap', 'augustus']
        estimate = estimate_total_memory_usage(contigs, tools)
        
        print(f"Total contigs: {len(contigs)}")
        print(f"Tools: {tools}")
        print(f"Total estimated peak memory: {estimate['total_predicted_peak_mb']:.1f} MB")
        
        print("\nPer-contig breakdown:")
        for contig_est in estimate['per_contig_estimates']:
            print(f"  {contig_est['contig']}: {contig_est['length_bp']:,} bp, peak: {contig_est['contig_peak_mb']:.1f} MB")

def test_memory_monitoring():
    """Test actual memory monitoring of a subprocess."""
    print("\n=== Testing Memory Monitoring ===")
    
    try:
        import subprocess
        
        # Test with a simple command that uses some memory
        print("Monitoring memory usage of 'sleep 2' command...")
        
        monitor = MemoryMonitor(sampling_interval=0.05)  # Sample every 50ms
        
        # Start a process
        process = subprocess.Popen(['sleep', '2'])
        
        # Monitor it
        stats = monitor.monitor_process(process, "sleep_test")
        
        print("Memory monitoring results:")
        print(format_memory_report(stats))
        
    except Exception as e:
        print(f"Memory monitoring test failed: {e}")

def main():
    """Run all tests."""
    print("Funannotate2 Memory Monitoring Test Suite")
    print("=" * 50)
    
    # Test all functionality
    test_memory_prediction()
    test_system_memory()
    test_cpu_allocation()
    test_total_estimation()
    test_memory_monitoring()
    
    print("\n=== Test Summary ===")
    print("✓ Memory prediction models")
    print("✓ System memory information")
    print("✓ CPU allocation suggestions")
    print("✓ Total memory estimation")
    print("✓ Real-time memory monitoring")
    print("\nAll tests completed successfully!")

if __name__ == "__main__":
    main()
