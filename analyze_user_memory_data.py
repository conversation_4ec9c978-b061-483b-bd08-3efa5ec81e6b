#!/usr/bin/env python3
"""
Analyze user's memory monitoring data to evaluate current prediction accuracy.
"""

import json
import sys
import statistics

def analyze_memory_data(jsonl_file):
    """Analyze memory monitoring data and compare with current predictions."""
    
    # Read all records
    records = []
    with open(jsonl_file, 'r') as f:
        for line in f:
            try:
                record = json.loads(line.strip())
                # Only include records with valid contig length data
                if record.get('contig_length_estimate') is not None and record.get('tool_name'):
                    records.append(record)
            except json.JSONDecodeError:
                continue
    
    print(f"Loaded {len(records)} valid records with contig length data")
    
    # Group by tool
    tools = {}
    for record in records:
        tool = record['tool_name']
        if tool not in tools:
            tools[tool] = []
        tools[tool].append(record)
    
    print(f"Found tools: {list(tools.keys())}")
    
    # Current prediction values (from our recent update)
    current_base_memory = {
        "snap": 234.0,
        "augustus": 250.0,
        "glimmerhmm": 356.0,
        "genemark": 200.0,
    }
    
    current_scaling_factor = {
        "snap": 149.0,
        "augustus": 150.0,
        "glimmerhmm": 164.0,
        "genemark": 140.0,
    }
    
    # Analyze each tool
    results = {}
    for tool, tool_records in tools.items():
        if len(tool_records) < 5:
            print(f"Skipping {tool}: insufficient data ({len(tool_records)} records)")
            continue
        
        # Extract data
        contig_lengths = [r['contig_length_estimate'] for r in tool_records]
        peak_memories = [r['memory_stats']['peak_rss_mb'] for r in tool_records]
        durations = [r['memory_stats']['duration_seconds'] for r in tool_records]
        
        # Calculate statistics
        min_length = min(contig_lengths)
        max_length = max(contig_lengths)
        avg_length = statistics.mean(contig_lengths)
        
        min_memory = min(peak_memories)
        max_memory = max(peak_memories)
        avg_memory = statistics.mean(peak_memories)
        median_memory = statistics.median(peak_memories)
        
        # Calculate scaling factor (memory increase per million base pairs)
        length_range_mbp = (max_length - min_length) / 1_000_000
        memory_range = max_memory - min_memory
        
        if length_range_mbp > 0:
            observed_scaling_mb_per_mbp = memory_range / length_range_mbp
        else:
            observed_scaling_mb_per_mbp = 0
        
        # Calculate memory per base pair for each record
        mb_per_bp = [mem / length for mem, length in zip(peak_memories, contig_lengths)]
        avg_mb_per_bp = statistics.mean(mb_per_bp)
        
        # Compare with current predictions
        current_base = current_base_memory.get(tool, 0)
        current_scaling = current_scaling_factor.get(tool, 0)
        
        # Calculate prediction accuracy for a typical contig (median length)
        median_length = statistics.median(contig_lengths)
        predicted_memory = current_base + (current_scaling * median_length / 1_000_000)
        actual_median_memory = median_memory
        prediction_error = abs(predicted_memory - actual_median_memory) / actual_median_memory * 100
        
        results[tool] = {
            'sample_size': len(tool_records),
            'contig_length_stats': {
                'min': min_length,
                'max': max_length,
                'avg': avg_length,
                'median': median_length
            },
            'memory_stats': {
                'min_mb': min_memory,
                'max_mb': max_memory,
                'avg_mb': avg_memory,
                'median_mb': median_memory
            },
            'observed_patterns': {
                'base_memory_estimate': min_memory * 0.9,  # Conservative estimate
                'scaling_mb_per_mbp': observed_scaling_mb_per_mbp,
                'avg_mb_per_mbp': avg_mb_per_bp * 1_000_000
            },
            'current_predictions': {
                'base_memory': current_base,
                'scaling_factor': current_scaling,
                'predicted_for_median': predicted_memory,
                'actual_median': actual_median_memory,
                'prediction_error_percent': prediction_error
            },
            'recommended_updates': {
                'base_memory': min_memory * 0.8,  # 20% buffer below minimum
                'scaling_factor': observed_scaling_mb_per_mbp * 1.2  # 20% safety margin
            }
        }
    
    return results

def print_analysis(results):
    """Print detailed analysis results."""
    
    print("\n" + "="*80)
    print("MEMORY USAGE ANALYSIS - USER DATA")
    print("="*80)
    
    for tool, data in results.items():
        print(f"\n{tool.upper()}:")
        print(f"  Sample size: {data['sample_size']} contigs")
        
        # Contig size range
        length_stats = data['contig_length_stats']
        print(f"  Contig length range: {length_stats['min']:,} - {length_stats['max']:,} bp")
        print(f"  Median contig length: {length_stats['median']:,} bp")
        
        # Memory usage
        mem_stats = data['memory_stats']
        print(f"  Memory usage range: {mem_stats['min_mb']:.1f} - {mem_stats['max_mb']:.1f} MB")
        print(f"  Median memory usage: {mem_stats['median_mb']:.1f} MB")
        
        # Current prediction accuracy
        current = data['current_predictions']
        print(f"  Current prediction accuracy:")
        print(f"    Predicted for median contig: {current['predicted_for_median']:.1f} MB")
        print(f"    Actual median usage: {current['actual_median']:.1f} MB")
        print(f"    Prediction error: {current['prediction_error_percent']:.1f}%")
        
        # Observed patterns
        observed = data['observed_patterns']
        print(f"  Observed scaling: {observed['scaling_mb_per_mbp']:.1f} MB per million BP")

def print_recommendations(results):
    """Print recommendations for updating prediction values."""
    
    print("\n" + "="*80)
    print("RECOMMENDATIONS FOR PREDICTION UPDATES")
    print("="*80)
    
    needs_update = False
    
    for tool, data in results.items():
        current = data['current_predictions']
        recommended = data['recommended_updates']
        
        print(f"\n{tool.upper()}:")
        
        # Check if update is needed (>20% error)
        if current['prediction_error_percent'] > 20:
            needs_update = True
            print(f"  ⚠️  NEEDS UPDATE (Error: {current['prediction_error_percent']:.1f}%)")
            print(f"  Current base memory: {current['base_memory']:.1f} MB")
            print(f"  Recommended base memory: {recommended['base_memory']:.1f} MB")
            print(f"  Current scaling: {current['scaling_factor']:.1f} MB/Mbp")
            print(f"  Recommended scaling: {recommended['scaling_factor']:.1f} MB/Mbp")
        else:
            print(f"  ✅ ACCURATE (Error: {current['prediction_error_percent']:.1f}%)")
            print(f"  Current values are sufficiently accurate")
    
    if needs_update:
        print(f"\n📝 UPDATED PYTHON CODE:")
        print("base_memory = {")
        for tool, data in results.items():
            recommended = data['recommended_updates']
            print(f'    "{tool}": {recommended["base_memory"]:.1f},')
        print("}")
        
        print("\nscaling_factor = {")
        for tool, data in results.items():
            recommended = data['recommended_updates']
            print(f'    "{tool}": {recommended["scaling_factor"]:.1f},')
        print("}")
    else:
        print(f"\n✅ No updates needed - current predictions are accurate!")

def main():
    jsonl_file = "/Users/<USER>/Downloads/memory-monitoring.jsonl"
    
    try:
        results = analyze_memory_data(jsonl_file)
        print_analysis(results)
        print_recommendations(results)
        
    except Exception as e:
        print(f"Error analyzing data: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
