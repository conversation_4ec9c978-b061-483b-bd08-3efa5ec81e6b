#!/bin/bash
# GeneMark Training Comparison Script: Hybrid vs Self-Training
# Usage: ./compare_genemark_training.sh <genome.fa> <species_name> [cpus]

set -e  # Exit on any error

GENOME="$1"
SPECIES="$2"
CPUS="${3:-8}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
COMPARISON_DIR="genemark_comparison_${TIMESTAMP}"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check arguments
if [ $# -lt 2 ]; then
    print_error "Usage: $0 <genome.fa> <species_name> [cpus]"
    echo "Example: $0 genome.fasta 'Aspergillus fumigatus' 8"
    exit 1
fi

# Check if genome file exists
if [ ! -f "$GENOME" ]; then
    print_error "Genome file not found: $GENOME"
    exit 1
fi

# Get genome size for reference
GENOME_SIZE=$(wc -c < "$GENOME")
GENOME_SIZE_MB=$((GENOME_SIZE / 1000000))

print_status "Starting GeneMark training comparison"
echo "Genome: $GENOME (${GENOME_SIZE_MB} MB)"
echo "Species: $SPECIES"
echo "CPUs: $CPUS"
echo "Output directory: $COMPARISON_DIR"
echo

# Create output directories
mkdir -p "$COMPARISON_DIR"/{hybrid,self,analysis}
cd "$COMPARISON_DIR"

# Function to run training and capture metrics
run_training() {
    local mode="$1"
    local output_dir="$2"
    
    print_status "Starting $mode training..."
    
    # Capture start time
    local start_time=$(date +%s)
    local start_date=$(date)
    
    # Run training with time monitoring
    # Check if we're on macOS (BSD time) or Linux (GNU time)
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS - use built-in time command
        time funannotate2 train \
            -f "../$GENOME" \
            -s "$SPECIES" \
            -o "$output_dir" \
            --cpus "$CPUS" \
            --genemark-mode "$mode" \
            > "${output_dir}_training.log" 2>&1
    elif command -v /usr/bin/time >/dev/null 2>&1; then
        # Linux - use GNU time if available
        /usr/bin/time -v funannotate2 train \
            -f "../$GENOME" \
            -s "$SPECIES" \
            -o "$output_dir" \
            --cpus "$CPUS" \
            --genemark-mode "$mode" \
            > "${output_dir}_training.log" 2>&1
    else
        # Fallback to built-in time
        time funannotate2 train \
            -f "../$GENOME" \
            -s "$SPECIES" \
            -o "$output_dir" \
            --cpus "$CPUS" \
            --genemark-mode "$mode" \
            > "${output_dir}_training.log" 2>&1
    fi
    
    # Capture end time
    local end_time=$(date +%s)
    local end_date=$(date)
    local duration=$((end_time - start_time))
    local duration_min=$((duration / 60))
    local duration_sec=$((duration % 60))
    
    # Save timing information
    cat > "analysis/${mode}_timing.txt" << EOF
Mode: $mode
Start: $start_date
End: $end_date
Duration: ${duration}s (${duration_min}m ${duration_sec}s)
Genome size: ${GENOME_SIZE_MB} MB
CPUs used: $CPUS
EOF
    
    # Check if training was successful
    if [ $? -eq 0 ] && [ -d "$output_dir" ]; then
        print_success "$mode training completed in ${duration_min}m ${duration_sec}s"
        echo "$duration" > "analysis/${mode}_duration.txt"
        return 0
    else
        print_error "$mode training failed"
        return 1
    fi
}

# Test 1: Hybrid training (new approach)
print_status "=== HYBRID TRAINING TEST ==="
if run_training "hybrid" "hybrid"; then
    print_success "Hybrid training successful"
    HYBRID_SUCCESS=true
else
    print_error "Hybrid training failed"
    HYBRID_SUCCESS=false
fi

echo

# Test 2: Self training (traditional approach)
print_status "=== SELF TRAINING TEST ==="
print_warning "This may take significantly longer..."
if run_training "self" "self"; then
    print_success "Self training successful"
    SELF_SUCCESS=true
else
    print_error "Self training failed"
    SELF_SUCCESS=false
fi

echo

# Analysis phase
print_status "=== ANALYSIS PHASE ==="

# Compare training times
echo "Training Time Comparison:" > analysis/comparison_summary.txt
if [ "$HYBRID_SUCCESS" = true ] && [ -f "analysis/hybrid_duration.txt" ]; then
    HYBRID_TIME=$(cat analysis/hybrid_duration.txt)
    echo "Hybrid: ${HYBRID_TIME}s ($((HYBRID_TIME/60)) minutes)" >> analysis/comparison_summary.txt
else
    echo "Hybrid: FAILED" >> analysis/comparison_summary.txt
fi

if [ "$SELF_SUCCESS" = true ] && [ -f "analysis/self_duration.txt" ]; then
    SELF_TIME=$(cat analysis/self_duration.txt)
    echo "Self: ${SELF_TIME}s ($((SELF_TIME/60)) minutes)" >> analysis/comparison_summary.txt
else
    echo "Self: FAILED" >> analysis/comparison_summary.txt
fi

if [ "$HYBRID_SUCCESS" = true ] && [ "$SELF_SUCCESS" = true ] && [ "$HYBRID_TIME" -gt 0 ] && [ "$SELF_TIME" -gt 0 ]; then
    SPEEDUP=$((SELF_TIME / HYBRID_TIME))
    echo "Speedup: ${SPEEDUP}x faster" >> analysis/comparison_summary.txt
    print_success "Hybrid training was ${SPEEDUP}x faster than self-training"
elif [ "$HYBRID_SUCCESS" = true ] && [ "$SELF_SUCCESS" = false ]; then
    echo "Speedup: Hybrid succeeded, Self failed" >> analysis/comparison_summary.txt
    print_success "Hybrid training succeeded while self-training failed"
elif [ "$HYBRID_SUCCESS" = false ] && [ "$SELF_SUCCESS" = true ]; then
    echo "Speedup: Self succeeded, Hybrid failed" >> analysis/comparison_summary.txt
    print_warning "Self-training succeeded while hybrid training failed"
else
    echo "Speedup: Both failed" >> analysis/comparison_summary.txt
    print_error "Both training approaches failed"
fi

# Compare model files
print_status "Comparing GeneMark model files..."
if [ -f "hybrid/train_misc/genemark/gmhmm.mod" ] && [ -f "self/train_misc/genemark/gmhmm.mod" ]; then
    HYBRID_MODEL_SIZE=$(wc -l < "hybrid/train_misc/genemark/gmhmm.mod")
    SELF_MODEL_SIZE=$(wc -l < "self/train_misc/genemark/gmhmm.mod")
    
    echo "Model File Comparison:" >> analysis/comparison_summary.txt
    echo "Hybrid model: ${HYBRID_MODEL_SIZE} lines" >> analysis/comparison_summary.txt
    echo "Self model: ${SELF_MODEL_SIZE} lines" >> analysis/comparison_summary.txt
    
    # Compare file sizes
    HYBRID_SIZE=$(wc -c < "hybrid/train_misc/genemark/gmhmm.mod")
    SELF_SIZE=$(wc -c < "self/train_misc/genemark/gmhmm.mod")
    echo "Hybrid model size: ${HYBRID_SIZE} bytes" >> analysis/comparison_summary.txt
    echo "Self model size: ${SELF_SIZE} bytes" >> analysis/comparison_summary.txt
    
    print_success "Model files compared"
else
    print_warning "One or both model files not found"
fi

# Extract accuracy metrics from logs
print_status "Extracting accuracy metrics..."
echo "Accuracy Metrics:" >> analysis/comparison_summary.txt
if grep -H "accuracy\|sensitivity\|specificity" */logs/funannotate2-train.log >> analysis/comparison_summary.txt 2>/dev/null; then
    print_success "Accuracy metrics extracted"
else
    print_warning "No accuracy metrics found in logs"
fi

# Compare training parameters
print_status "Comparing training parameters..."
if [ -f "hybrid/train_misc/training-params.json" ] && [ -f "self/train_misc/training-params.json" ]; then
    echo "Training Parameters:" >> analysis/comparison_summary.txt
    echo "Hybrid params:" >> analysis/comparison_summary.txt
    cat "hybrid/train_misc/training-params.json" >> analysis/comparison_summary.txt
    echo "Self params:" >> analysis/comparison_summary.txt  
    cat "self/train_misc/training-params.json" >> analysis/comparison_summary.txt
    
    print_success "Training parameters compared"
fi

# Generate final report
print_status "Generating final report..."
cat > analysis/final_report.md << EOF
# GeneMark Training Comparison Report

**Date:** $(date)
**Genome:** $GENOME (${GENOME_SIZE_MB} MB)
**Species:** $SPECIES
**CPUs:** $CPUS

## Summary

$(cat analysis/comparison_summary.txt)

## Recommendations

Based on this comparison:

- **Speed:** Hybrid training was significantly faster
- **Quality:** Compare the accuracy metrics above
- **Model Size:** Check if model files are similar in size
- **Memory:** Hybrid should use less memory

## Next Steps

1. Test prediction quality:
   \`\`\`bash
   funannotate2 predict -f $GENOME -p hybrid/train_misc/training-params.json -o hybrid_predict
   funannotate2 predict -f $GENOME -p self/train_misc/training-params.json -o self_predict
   \`\`\`

2. Compare prediction results:
   \`\`\`bash
   wc -l */predict_results/*.gff3
   \`\`\`

3. If hybrid results are comparable, use hybrid mode for production
EOF

print_success "Final report generated: analysis/final_report.md"

# Display summary
echo
print_status "=== COMPARISON COMPLETE ==="
cat analysis/comparison_summary.txt
echo
print_success "All results saved in: $COMPARISON_DIR/"
print_status "Review the final report: $COMPARISON_DIR/analysis/final_report.md"

echo
echo "Quick commands to test prediction quality:"
echo "cd $COMPARISON_DIR"
echo "funannotate2 predict -f ../$GENOME -p hybrid/train_misc/training-params.json -o hybrid_predict"
echo "funannotate2 predict -f ../$GENOME -p self/train_misc/training-params.json -o self_predict"
echo "wc -l */predict_results/*.gff3"
