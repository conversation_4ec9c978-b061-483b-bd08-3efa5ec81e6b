#!/usr/bin/env python3
"""
Debug script to test multiprocessing behavior with abinitio_wrapper.
"""

import os
import tempfile
import multiprocessing
from funannotate2.utilities import runProcessJob

def simple_test_function(contig, params, logger, monitor_memory, memory_limit_gb, transcript_alignments, protein_alignments):
    """Simple test function that mimics abinitio_wrapper signature."""
    print(f"Processing contig: {os.path.basename(contig)}")
    print(f"Params keys: {list(params.keys())}")
    print(f"Monitor memory: {monitor_memory}")
    print(f"Memory limit: {memory_limit_gb}")
    print(f"Transcript alignments: {transcript_alignments}")
    print(f"Protein alignments: {protein_alignments}")
    return f"Processed {os.path.basename(contig)}"

def test_multiprocessing():
    """Test multiprocessing with the exact parameter structure."""
    
    # Create test files
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create test contig files
        contigs = []
        for i in range(3):
            contig_file = os.path.join(tmpdir, f"contig_{i}.fasta")
            with open(contig_file, 'w') as f:
                f.write(f">contig_{i}\nATGCGCGCGCGCTAG\n")
            contigs.append(contig_file)
        
        # Create mock params
        params = {"abinitio": {}}
        
        # Create mock logger
        class MockLogger:
            def info(self, msg):
                print(f"INFO: {msg}")
            def warning(self, msg):
                print(f"WARNING: {msg}")
            def error(self, msg):
                print(f"ERROR: {msg}")
        
        logger = MockLogger()
        
        # Build command list exactly like predict.py does
        abinit_cmds = []
        for c in contigs:
            abinit_cmds.append((
                c,                    # contig
                params,               # params
                logger,               # logger
                False,                # monitor_memory
                None,                 # memory_limit_gb
                None,                 # transcript_alignments
                None                  # protein_alignments
            ))
        
        print(f"Created {len(abinit_cmds)} commands")
        print("Testing multiprocessing...")
        
        try:
            # Test with simple function first
            results = runProcessJob(simple_test_function, abinit_cmds, cpus=2)
            print(f"✅ Simple test succeeded! Results: {results}")
            
            # Now test with actual abinitio_wrapper
            print("\nTesting with actual abinitio_wrapper...")
            from funannotate2.predict import abinitio_wrapper
            
            results = runProcessJob(abinitio_wrapper, abinit_cmds, cpus=2)
            print(f"✅ abinitio_wrapper test succeeded! Results: {results}")
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    print("Testing multiprocessing behavior...")
    test_multiprocessing()
