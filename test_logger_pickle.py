#!/usr/bin/env python3
"""
Test if the logger can be pickled for multiprocessing.
"""

import pickle
import tempfile
import os
from funannotate2.log import startLogging

def test_logger_pickle():
    """Test if the logger from startLogging can be pickled."""
    
    with tempfile.TemporaryDirectory() as tmpdir:
        log_file = os.path.join(tmpdir, "test.log")
        
        # Create logger the same way as predict.py
        logger = startLogging(logfile=log_file)
        
        print(f"Logger type: {type(logger)}")
        print(f"Logger attributes: {dir(logger)}")
        
        try:
            # Try to pickle the logger
            pickled = pickle.dumps(logger)
            print("✅ Logger can be pickled!")
            
            # Try to unpickle
            unpickled = pickle.loads(pickled)
            print("✅ Logger can be unpickled!")
            
            # Test if it works
            unpickled.info("Test message")
            print("✅ Unpickled logger works!")
            
            return True
            
        except Exception as e:
            print(f"❌ Logger cannot be pickled: {e}")
            return False

if __name__ == "__main__":
    print("Testing logger pickling...")
    test_logger_pickle()
