[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "funannotate2"
version = "25.7.1"
description = "Funannotate2: eukarytoic genome annotation pipeline"
readme = {file = "README.md", content-type = "text/markdown"}
authors = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
requires-python = ">=3.7.0"
dependencies = [
    "natsort",
    "numpy",
    "mappy",
    "gfftk>=25.6.10",
    "buscolite>=25.4.24",
    "gapmm2>=25.4.13",
    "pyhmmer>=0.10.15",
    "pyfastx>=2.0.0",
    "requests",
    "gb-io>=0.3.2",
    "json-repair",
    "pytantan",
    "psutil"
]
license = {file = "LICENSE"}
classifiers = [
    "Development Status :: 4 - Beta",
    "License :: OSI Approved :: BSD License",
    "Programming Language :: Python",
    "Operating System :: Unix",
    "Intended Audience :: Science/Research",
    "Topic :: Scientific/Engineering :: Bio-Informatics",
]
keywords = ["bioinformatics", "genome", "annotation"]

[project.urls]
Homepage = "https://github.com/nextgenusfs/funannotate2"
Repository = "https://github.com/nextgenusfs/funannotate2.git"

[project.scripts]
funannotate2 = "funannotate2.__main__:main"

[tool.hatch.build]
include = [
  "funannotate2/*.py",
  "funannotate2/downloads.json",
  "funannotate2/resources/*",
  "README.md",
  "LICENSE.md"
]
exclude = [
  "tests/*",
  "local_tests/*"
]
