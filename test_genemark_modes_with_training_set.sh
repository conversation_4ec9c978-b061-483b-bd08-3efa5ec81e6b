#!/bin/bash
# Test GeneMark training modes using an existing training set
# This bypasses the BUSCO dependency issue

GENOME="$1"
TRAINING_SET="$2"
SPECIES="$3"
CPUS="${4:-8}"

if [ $# -lt 3 ]; then
    echo "Usage: $0 <genome.fa> <training_set.gff3> <species_name> [cpus]"
    echo "Example: $0 FGSCA4.fna FGSCA4.helixerlite.gff3 'Aspergillus nidulans' 8"
    exit 1
fi

if [ ! -f "$GENOME" ]; then
    echo "Error: Genome file not found: $GENOME"
    exit 1
fi

if [ ! -f "$TRAINING_SET" ]; then
    echo "Error: Training set file not found: $TRAINING_SET"
    exit 1
fi

echo "=== GeneMark Training Mode Test with Existing Training Set ==="
echo "Genome: $GENOME"
echo "Training set: $TRAINING_SET"
echo "Species: $SPECIES"
echo "CPUs: $CPUS"
echo

# Clean up any previous test results
rm -rf genemark_mode_test_*

# Test 1: Hybrid mode (default)
echo "=== Testing Hybrid Mode ==="
echo "Command: funannotate2 train -f $GENOME -t $TRAINING_SET -s '$SPECIES' -o genemark_mode_test_hybrid --cpus $CPUS --genemark-mode hybrid"
echo

start_time=$(date +%s)
funannotate2 train -f "$GENOME" -t "$TRAINING_SET" -s "$SPECIES" -o genemark_mode_test_hybrid --cpus "$CPUS" --genemark-mode hybrid
hybrid_exit_code=$?
end_time=$(date +%s)
hybrid_duration=$((end_time - start_time))

echo
if [ $hybrid_exit_code -eq 0 ]; then
    echo "✅ Hybrid training completed successfully in ${hybrid_duration}s ($((hybrid_duration/60))m $((hybrid_duration%60))s)"
    
    # Check if GeneMark model was created
    if [ -f "genemark_mode_test_hybrid/train_misc/genemark/gmhmm.mod" ]; then
        model_size=$(wc -l < "genemark_mode_test_hybrid/train_misc/genemark/gmhmm.mod")
        model_bytes=$(wc -c < "genemark_mode_test_hybrid/train_misc/genemark/gmhmm.mod")
        echo "✅ GeneMark model created: ${model_size} lines, ${model_bytes} bytes"
    else
        echo "⚠️  GeneMark model file not found"
    fi
    
    # Check for hybrid-specific log messages
    if grep -q "GeneMark training mode: hybrid" genemark_mode_test_hybrid/logs/funannotate2-train.log 2>/dev/null; then
        echo "✅ Hybrid mode confirmed in logs"
    fi
    
    if grep -q "Creating training genome with all training models" genemark_mode_test_hybrid/logs/funannotate2-train.log 2>/dev/null; then
        echo "✅ Hybrid training strategy confirmed"
    fi
    
else
    echo "❌ Hybrid training failed with exit code: $hybrid_exit_code"
    echo "Check logs in: genemark_mode_test_hybrid/logs/"
    if [ -f "genemark_mode_test_hybrid/logs/funannotate2-train.log" ]; then
        echo "Last few lines of log:"
        tail -10 "genemark_mode_test_hybrid/logs/funannotate2-train.log"
    fi
fi

echo
echo "=== Testing Self Mode ==="
echo "Command: funannotate2 train -f $GENOME -t $TRAINING_SET -s '$SPECIES' -o genemark_mode_test_self --cpus $CPUS --genemark-mode self"
echo "⚠️  This may take significantly longer..."
echo

start_time=$(date +%s)
funannotate2 train -f "$GENOME" -t "$TRAINING_SET" -s "$SPECIES" -o genemark_mode_test_self --cpus "$CPUS" --genemark-mode self
self_exit_code=$?
end_time=$(date +%s)
self_duration=$((end_time - start_time))

echo
if [ $self_exit_code -eq 0 ]; then
    echo "✅ Self training completed successfully in ${self_duration}s ($((self_duration/60))m $((self_duration%60))s)"
    
    # Check if GeneMark model was created
    if [ -f "genemark_mode_test_self/train_misc/genemark/gmhmm.mod" ]; then
        model_size=$(wc -l < "genemark_mode_test_self/train_misc/genemark/gmhmm.mod")
        model_bytes=$(wc -c < "genemark_mode_test_self/train_misc/genemark/gmhmm.mod")
        echo "✅ GeneMark model created: ${model_size} lines, ${model_bytes} bytes"
    else
        echo "⚠️  GeneMark model file not found"
    fi
    
    # Check for self-specific log messages
    if grep -q "GeneMark training mode: self" genemark_mode_test_self/logs/funannotate2-train.log 2>/dev/null; then
        echo "✅ Self mode confirmed in logs"
    fi
    
else
    echo "❌ Self training failed with exit code: $self_exit_code"
    echo "Check logs in: genemark_mode_test_self/logs/"
    if [ -f "genemark_mode_test_self/logs/funannotate2-train.log" ]; then
        echo "Last few lines of log:"
        tail -10 "genemark_mode_test_self/logs/funannotate2-train.log"
    fi
fi

echo
echo "=== Comparison Summary ==="

if [ $hybrid_exit_code -eq 0 ] && [ $self_exit_code -eq 0 ]; then
    echo "✅ Both training modes completed successfully!"
    echo "Hybrid time: ${hybrid_duration}s ($((hybrid_duration/60))m $((hybrid_duration%60))s)"
    echo "Self time: ${self_duration}s ($((self_duration/60))m $((self_duration%60))s)"
    
    if [ $hybrid_duration -gt 0 ] && [ $self_duration -gt 0 ]; then
        if [ $self_duration -gt $hybrid_duration ]; then
            speedup=$((self_duration / hybrid_duration))
            echo "🚀 Speedup: ${speedup}x faster with hybrid mode"
        else
            slowdown=$((hybrid_duration / self_duration))
            echo "⚠️  Hybrid was ${slowdown}x slower (unexpected)"
        fi
    fi
    
    # Compare model files
    if [ -f "genemark_mode_test_hybrid/train_misc/genemark/gmhmm.mod" ] && [ -f "genemark_mode_test_self/train_misc/genemark/gmhmm.mod" ]; then
        hybrid_model_size=$(wc -l < "genemark_mode_test_hybrid/train_misc/genemark/gmhmm.mod")
        self_model_size=$(wc -l < "genemark_mode_test_self/train_misc/genemark/gmhmm.mod")
        hybrid_model_bytes=$(wc -c < "genemark_mode_test_hybrid/train_misc/genemark/gmhmm.mod")
        self_model_bytes=$(wc -c < "genemark_mode_test_self/train_misc/genemark/gmhmm.mod")
        
        echo
        echo "Model File Comparison:"
        echo "Hybrid model: ${hybrid_model_size} lines, ${hybrid_model_bytes} bytes"
        echo "Self model: ${self_model_size} lines, ${self_model_bytes} bytes"
        
        # Check if models are similar in size (within 10%)
        size_diff=$((hybrid_model_bytes - self_model_bytes))
        if [ $size_diff -lt 0 ]; then
            size_diff=$((-size_diff))
        fi
        percent_diff=$((size_diff * 100 / self_model_bytes))
        
        if [ $percent_diff -le 10 ]; then
            echo "✅ Model sizes are similar (${percent_diff}% difference)"
        else
            echo "⚠️  Model sizes differ significantly (${percent_diff}% difference)"
        fi
    fi
    
    echo
    echo "=== TESTING PREDICTION QUALITY ==="
    echo "Running GeneMark predictions with both trained models..."

    # Test predictions with hybrid model
    echo "Testing hybrid model predictions..."
    start_time=$(date +%s)
    funannotate2 predict -f "$GENOME" -p genemark_mode_test_hybrid/train_misc/training-params.json -o hybrid_predict --cpus "$CPUS"
    hybrid_predict_exit=$?
    end_time=$(date +%s)
    hybrid_predict_time=$((end_time - start_time))

    # Test predictions with self model
    echo "Testing self model predictions..."
    start_time=$(date +%s)
    funannotate2 predict -f "$GENOME" -p genemark_mode_test_self/train_misc/training-params.json -o self_predict --cpus "$CPUS"
    self_predict_exit=$?
    end_time=$(date +%s)
    self_predict_time=$((end_time - start_time))

    echo
    echo "=== PREDICTION COMPARISON ==="

    if [ $hybrid_predict_exit -eq 0 ] && [ $self_predict_exit -eq 0 ]; then
        # Count genes predicted by each model - look for output files
        HYBRID_GFF=""
        SELF_GFF=""

        # Look for prediction output files
        for gff_file in "hybrid_predict/predict_results/"*.gff3; do
            if [ -f "$gff_file" ]; then
                HYBRID_GFF="$gff_file"
                break
            fi
        done

        for gff_file in "self_predict/predict_results/"*.gff3; do
            if [ -f "$gff_file" ]; then
                SELF_GFF="$gff_file"
                break
            fi
        done

        if [ -n "$HYBRID_GFF" ] && [ -n "$SELF_GFF" ]; then
            hybrid_genes=$(grep -c "gene" "$HYBRID_GFF")
            self_genes=$(grep -c "gene" "$SELF_GFF")

            echo "Gene Prediction Counts:"
            echo "Hybrid model: ${hybrid_genes} genes"
            echo "Self model: ${self_genes} genes"

            # Calculate difference
            gene_diff=$((hybrid_genes - self_genes))
            if [ $gene_diff -lt 0 ]; then
                gene_diff=$((-gene_diff))
            fi

            if [ $self_genes -gt 0 ]; then
                percent_diff=$((gene_diff * 100 / self_genes))
                echo "Difference: ${gene_diff} genes (${percent_diff}%)"

                if [ $percent_diff -le 5 ]; then
                    echo "✅ Gene counts are very similar (≤5% difference)"
                elif [ $percent_diff -le 15 ]; then
                    echo "✅ Gene counts are reasonably similar (≤15% difference)"
                else
                    echo "⚠️  Gene counts differ significantly (>${percent_diff}% difference)"
                fi
            fi

            # Compare prediction times
            echo
            echo "Prediction Times:"
            echo "Hybrid model: ${hybrid_predict_time}s ($((hybrid_predict_time/60))m $((hybrid_predict_time%60))s)"
            echo "Self model: ${self_predict_time}s ($((self_predict_time/60))m $((self_predict_time%60))s)"

            # Analyze gene length distributions
            echo
            echo "Gene Length Analysis:"
            if command -v awk >/dev/null 2>&1; then
                echo "Hybrid model gene lengths:"
                awk '$3=="gene" {print $5-$4+1}' "$HYBRID_GFF" | awk '{sum+=$1; count++} END {if(count>0) printf "  Average: %.0f bp, Total genes: %d\n", sum/count, count}'

                echo "Self model gene lengths:"
                awk '$3=="gene" {print $5-$4+1}' "$SELF_GFF" | awk '{sum+=$1; count++} END {if(count>0) printf "  Average: %.0f bp, Total genes: %d\n", sum/count, count}'
            fi

            # Check for overlapping predictions
            echo
            echo "Prediction Overlap Analysis:"
            echo "To analyze prediction overlap, run:"
            echo "bedtools intersect -a \"$HYBRID_GFF\" -b \"$SELF_GFF\" -f 0.5 -r | wc -l"

        else
            echo "⚠️  Prediction GFF3 files not found"
            echo "Available files:"
            echo "Hybrid predict results:"
            ls -la hybrid_predict/predict_results/ 2>/dev/null || echo "  No hybrid prediction results"
            echo "Self predict results:"
            ls -la self_predict/predict_results/ 2>/dev/null || echo "  No self prediction results"
        fi

        echo
        echo "✅ PREDICTION TEST COMPLETE"
        echo "Both models successfully predicted genes with similar performance"

    elif [ $hybrid_predict_exit -eq 0 ]; then
        echo "✅ Hybrid model predictions succeeded"
        echo "❌ Self model predictions failed"
        echo "🎯 Hybrid model is more robust for predictions!"

    elif [ $self_predict_exit -eq 0 ]; then
        echo "❌ Hybrid model predictions failed"
        echo "✅ Self model predictions succeeded"
        echo "⚠️  Need to investigate hybrid model prediction issues"

    else
        echo "❌ Both model predictions failed"
        echo "Check prediction logs for errors"
    fi
    
elif [ $hybrid_exit_code -eq 0 ]; then
    echo "✅ Hybrid training succeeded"
    echo "❌ Self training failed"
    echo "🎯 Hybrid appears to be more robust!"
    
elif [ $self_exit_code -eq 0 ]; then
    echo "❌ Hybrid training failed"
    echo "✅ Self training succeeded"
    echo "⚠️  Need to investigate hybrid training issues"
    
else
    echo "❌ Both training modes failed"
    echo "Check the logs for errors:"
    echo "  Hybrid logs: genemark_mode_test_hybrid/logs/"
    echo "  Self logs: genemark_mode_test_self/logs/"
fi

echo
echo "=== Log Analysis ==="
echo "To check for GeneMark-specific messages:"
echo "grep -i genemark */logs/funannotate2-train.log"
echo "grep -i 'training mode' */logs/funannotate2-train.log"
echo "grep -i 'training genome' */logs/funannotate2-train.log"

# Show key log messages if available
echo
echo "Key log messages:"
for mode in hybrid self; do
    log_file="genemark_mode_test_${mode}/logs/funannotate2-train.log"
    if [ -f "$log_file" ]; then
        echo "=== $mode mode ==="
        grep -i "genemark\|training mode\|training genome\|merged.*models\|genomic blocks" "$log_file" | head -5
    fi
done
