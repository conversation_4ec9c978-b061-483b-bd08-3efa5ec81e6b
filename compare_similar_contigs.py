#!/usr/bin/env python3
"""
Compare memory usage for contigs similar in size to our local test data.
"""

import json
import statistics


def analyze_similar_sized_contigs():
    """Analyze memory usage for contigs similar to our test data (2.8-4.9 Mbp)."""

    jsonl_file = "/Users/<USER>/Downloads/memory-monitoring.jsonl"

    # First, let's see the size distribution
    all_records = []
    with open(jsonl_file, "r") as f:
        for line in f:
            try:
                record = json.loads(line.strip())
                if record.get("contig_length_estimate") is not None and record.get(
                    "tool_name"
                ):
                    all_records.append(record)
            except json.JSONDecodeError:
                continue

    # Analyze size distribution
    all_lengths = [r["contig_length_estimate"] for r in all_records]
    all_lengths.sort()

    print("CONTIG SIZE DISTRIBUTION:")
    print(f"Total contigs: {len(all_lengths)}")
    print(f"Smallest: {min(all_lengths):,} bp")
    print(f"Largest: {max(all_lengths):,} bp")
    print(f"Median: {statistics.median(all_lengths):,} bp")
    print(f"Average: {statistics.mean(all_lengths):,.0f} bp")

    # Show percentiles
    percentiles = [50, 75, 90, 95, 99]
    for p in percentiles:
        idx = int(len(all_lengths) * p / 100)
        print(f"{p}th percentile: {all_lengths[idx]:,} bp")

    # Our local test range was 2.8-4.9 Mbp
    local_test_min = 2_800_000  # 2.8 Mbp
    local_test_max = 4_900_000  # 4.9 Mbp

    # Count how many are in our test range
    in_range = [l for l in all_lengths if local_test_min <= l <= local_test_max]
    print(f"\nContigs in our test range (2.8-4.9 Mbp): {len(in_range)}")

    # Let's try a broader range around our test data
    broader_min = 1_000_000  # 1 Mbp
    broader_max = 10_000_000  # 10 Mbp
    broader_range = [l for l in all_lengths if broader_min <= l <= broader_max]
    print(f"Contigs in broader range (1-10 Mbp): {len(broader_range)}")

    # Find the largest contigs to compare with
    large_contigs = [l for l in all_lengths if l >= 1_000_000]  # >= 1 Mbp
    print(f"Contigs >= 1 Mbp: {len(large_contigs)}")

    if len(large_contigs) == 0:
        print("No large contigs found for comparison!")
        return

    # Use the broader range for analysis
    records = []
    with open(jsonl_file, "r") as f:
        for line in f:
            try:
                record = json.loads(line.strip())
                if (
                    record.get("contig_length_estimate") is not None
                    and record.get("tool_name")
                    and record["contig_length_estimate"] >= 1_000_000
                ):  # >= 1 Mbp
                    records.append(record)
            except json.JSONDecodeError:
                continue

    print(f"\nAnalyzing {len(records)} records with contigs >= 1 Mbp")

    # Group by tool
    tools = {}
    for record in records:
        tool = record["tool_name"]
        if tool not in tools:
            tools[tool] = []
        tools[tool].append(record)

    print(f"Tools with similar-sized contigs: {list(tools.keys())}")

    # Our original measurements (from local test data)
    original_measurements = {
        "snap": {"base": 234.0, "scaling": 149.0, "sample_range": "2.8-4.9 Mbp"},
        "glimmerhmm": {"base": 356.0, "scaling": 164.0, "sample_range": "2.8-4.9 Mbp"},
        "augustus": {
            "base": 250.0,
            "scaling": 150.0,
            "sample_range": "2.8-4.9 Mbp (Docker estimate)",
        },
        "genemark": {
            "base": 200.0,
            "scaling": 140.0,
            "sample_range": "2.8-4.9 Mbp (Docker estimate)",
        },
    }

    print("\n" + "=" * 80)
    print("COMPARISON: LOCAL TEST DATA vs USER DATA (SIMILAR CONTIG SIZES)")
    print("=" * 80)

    for tool, tool_records in tools.items():
        if len(tool_records) < 3:
            print(f"\nSkipping {tool}: insufficient data ({len(tool_records)} records)")
            continue

        print(f"\n{tool.upper()}:")
        print(f"  Sample size: {len(tool_records)} contigs")

        # Extract data
        contig_lengths = [r["contig_length_estimate"] for r in tool_records]
        peak_memories = [r["memory_stats"]["peak_rss_mb"] for r in tool_records]

        # Calculate statistics
        min_length = min(contig_lengths)
        max_length = max(contig_lengths)
        avg_length = statistics.mean(contig_lengths)
        median_length = statistics.median(contig_lengths)

        min_memory = min(peak_memories)
        max_memory = max(peak_memories)
        avg_memory = statistics.mean(peak_memories)
        median_memory = statistics.median(peak_memories)

        print(f"  Contig length range: {min_length:,} - {max_length:,} bp")
        print(f"  Average contig length: {avg_length:,.0f} bp")
        print(f"  Memory usage range: {min_memory:.1f} - {max_memory:.1f} MB")
        print(f"  Average memory usage: {avg_memory:.1f} MB")
        print(f"  Median memory usage: {median_memory:.1f} MB")

        # Calculate observed scaling for this size range
        if len(tool_records) > 1:
            length_range_mbp = (max_length - min_length) / 1_000_000
            memory_range = max_memory - min_memory

            if length_range_mbp > 0:
                observed_scaling = memory_range / length_range_mbp
                print(f"  Observed scaling: {observed_scaling:.1f} MB per million BP")
            else:
                print(f"  Observed scaling: Cannot calculate (insufficient range)")

        # Compare with our original measurements
        if tool in original_measurements:
            original = original_measurements[tool]
            print(f"\n  COMPARISON WITH LOCAL TEST DATA:")
            print(f"    Original base memory: {original['base']:.1f} MB")
            print(f"    User data min memory: {min_memory:.1f} MB")
            print(
                f"    Difference: {((min_memory / original['base']) - 1) * 100:+.1f}%"
            )

            print(f"    Original scaling: {original['scaling']:.1f} MB/Mbp")
            if "observed_scaling" in locals():
                print(f"    User data scaling: {observed_scaling:.1f} MB/Mbp")
                print(
                    f"    Scaling difference: {((observed_scaling / original['scaling']) - 1) * 100:+.1f}%"
                )

            # Predict memory for average contig size using our formula
            predicted_memory = original["base"] + (
                original["scaling"] * avg_length / 1_000_000
            )
            print(
                f"    Predicted for avg contig ({avg_length:,.0f} bp): {predicted_memory:.1f} MB"
            )
            print(f"    Actual average usage: {avg_memory:.1f} MB")
            error_percent = abs(predicted_memory - avg_memory) / avg_memory * 100
            print(f"    Prediction error: {error_percent:.1f}%")

            if error_percent < 20:
                print(f"    ✅ GOOD ACCURACY (< 20% error)")
            elif error_percent < 50:
                print(f"    ⚠️  MODERATE ERROR (20-50% error)")
            else:
                print(f"    ❌ POOR ACCURACY (> 50% error)")
        else:
            print(f"    No original measurements for {tool}")

    # Overall summary
    print(f"\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)

    total_similar_contigs = len(records)
    all_lengths = [r["contig_length_estimate"] for r in records]
    all_memories = [r["memory_stats"]["peak_rss_mb"] for r in records]

    print(f"Total contigs in test range: {total_similar_contigs}")
    print(f"Average contig size: {statistics.mean(all_lengths):,.0f} bp")
    print(f"Average memory usage: {statistics.mean(all_memories):.1f} MB")
    print(f"Memory usage range: {min(all_memories):.1f} - {max(all_memories):.1f} MB")


def main():
    try:
        analyze_similar_sized_contigs()
    except Exception as e:
        print(f"Error analyzing data: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
